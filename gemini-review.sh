#!/usr/bin/env bash
# gemini-review.sh — Run unit tests then invoke Gemini CLI for code review in read-only mode.
# This script is intended to be executed locally or in CI.
# 1. Executes `npm run test` to ensure unit tests pass.
# 2. If tests succeed, creates a patch diff against origin/main.
# 3. Invokes Gemini CLI with the patch and review guide, writing feedback to gemini_feedback.md.
# 4. Exits non-zero when tests fail or the gemini CLI invocation fails.

set -euo pipefail

# Relative path to the code-review checklist file.
GUIDE="docs/code-review-checklist.md"

if [[ ! -f "$GUIDE" ]]; then
  echo "[gemini-review] Guide file '$GUIDE' not found. Aborting." >&2
  exit 1
fi

echo "[gemini-review] Running unit tests…"
if ! npm run test; then
  echo "[gemini-review] Unit tests failed — fix tests before requesting review." >&2
  exit 1
fi

echo "[gemini-review] All tests passed. Generating diff against origin/main…"
PATCH_FILE="$(mktemp)"
# Capture diff of current HEAD against origin/main (change as needed for your default branch)
git diff origin/main...HEAD > "$PATCH_FILE"

if [[ ! -s "$PATCH_FILE" ]]; then
  echo "[gemini-review] No diff detected between HEAD and origin/main. Nothing to review." >&2
  exit 0
fi

echo "[gemini-review] Invoking Gemini CLI in read-only mode…"

gemini --prompt "\nAct as a staff engineer.\nReview @${PATCH_FILE} against @${GUIDE}.\nReturn GitHub-flavored markdown feedback.\n" --quiet > gemini_feedback.md

echo "[gemini-review] Review complete ➜ gemini_feedback.md"