2025-07-21 20:57:10,257 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getMe "HTTP/1.1 200 OK"
2025-07-21 20:57:10,259 - INFO - Telegram bot connected: @gh_coding_agent_bot
2025-07-21 20:57:10,420 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getMe "HTTP/1.1 200 OK"
2025-07-21 20:57:10,425 - INFO - Application started
2025-07-21 20:57:10,973 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/sendMessage "HTTP/1.1 200 OK"
2025-07-21 20:57:10,979 - INFO - Telegram bot started successfully
2025-07-21 20:57:10,981 - INFO - Launching Claude CLI: npx @anthropic-ai/claude-code run
2025-07-21 20:57:11,317 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/deleteWebhook "HTTP/1.1 200 OK"
2025-07-21 20:57:13,239 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/sendMessage "HTTP/1.1 200 OK"
2025-07-21 20:57:13,241 - INFO - Created new live view message 465
2025-07-21 20:57:13,748 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/sendMessage "HTTP/1.1 200 OK"
2025-07-21 20:57:13,749 - INFO - Created new live view message 466
2025-07-21 20:57:15,119 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:16,499 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:17,866 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:19,137 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 409 Conflict"
2025-07-21 20:57:19,141 - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 20:57:19,231 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:20,610 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:21,999 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:23,373 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:24,766 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:26,173 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:27,539 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:28,901 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:30,289 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:30,321 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 200 OK"
2025-07-21 20:57:31,657 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:33,027 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:34,409 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:35,784 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:37,153 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:38,548 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:39,933 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:40,494 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 200 OK"
2025-07-21 20:57:41,350 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:42,734 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:44,162 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:45,562 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:46,947 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:48,358 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:49,769 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:50,815 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 200 OK"
2025-07-21 20:57:51,151 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:52,594 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:53,980 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:55,376 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 20:57:55,766 - INFO - Claude CLI exited with code 0
2025-07-21 20:57:55,767 - INFO - ClaudeStreamer stopped.
2025-07-21 20:57:55,768 - INFO - ClaudeStreamer stopped.
2025-07-21 20:57:55,769 - INFO - Stopping Telegram bot...
2025-07-21 20:57:55,770 - INFO - Application is stopping. This might take a moment.
2025-07-21 20:57:55,771 - INFO - Application.stop() complete
2025-07-21 20:57:55,772 - ERROR - Error stopping Telegram application: This Updater is still running!
2025-07-21 20:57:55,773 - INFO - Telegram bot stopped
2025-07-21 20:57:55,790 - ERROR - Task exception was never retrieved
future: <Task finished name='Task-23' coro=<Updater._start_polling.<locals>.polling_action_cb() done, defined at /Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py:338> exception=NetworkError('httpx.ReadError: ')>
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_backends/anyio.py", line 32, in read
    with map_exceptions(exc_map):
  File "/Users/<USER>/.pyenv/versions/3.10.1/lib/python3.10/contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "/Users/<USER>/.pyenv/versions/3.10.1/lib/python3.10/contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ReadError: 
