#!/usr/bin/env python3
"""
RemoCode debug launcher with verbose logging.
"""

import os
import sys
from pathlib import Path

# Enable debug logging
os.environ["LOG_LEVEL"] = "DEBUG"

# Add src directory to Python path
script_dir = Path(__file__).parent
parent_dir = script_dir
sys.path.insert(0, str(parent_dir))

# Import and run main application
try:
    from src.main import main as _main
    _main()
except ImportError as e:
    print(f"❌ Failed to import RemoCode modules: {e}")
    print("💡 Make sure you've installed dependencies: uv pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ Fatal error: {e}")
    sys.exit(1)