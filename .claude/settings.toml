[[hooks]]
# RemoCode Integration: Track <PERSON>'s Start event
event = "Start"
run_in_background = false
command = """
  echo '{"event": "Start", "timestamp": "'$(date -Iseconds)'"}' > .remocode_state.json
"""

[[hooks]]
# RemoCode Integration: Track when <PERSON> starts using tools
event = "ToolUse"
run_in_background = false
command = """
  echo '{"event": "ToolUse", "timestamp": "'$(date -Iseconds)'"}' > .remocode_state.json
"""

[[hooks]]
# RemoCode Integration: Detect user prompts and menu selections 
event = "UserPromptSubmit"
run_in_background = false
command = """
  python3 remocode/v2/hooks/menu_detector.py "$CLAUDE_USER_PROMPT"
"""

[[hooks]]
# RemoCode Integration: Detect notification prompts (when <PERSON> waits for input)
event = "Notification"
run_in_background = false
command = """
  python3 remocode/v2/hooks/notification_handler.py "$CLAUDE_NOTIFICATION_TYPE" "$CLAUDE_NOTIFICATION_MESSAGE"
"""

[[hooks]]
# RemoCode Integration: Track when <PERSON> finishes and is about to send final reply
event = "Stop"
run_in_background = false
command = """
  echo '{"event": "Stop", "timestamp": "'$(date -Iseconds)'"}' > .remocode_state.json && \
  echo '🔍  Running unit tests…'           && \
  npm run test                             && \
  echo '🤖  Running Gemini code review…'   && \
  PATCH=$(mktemp)                          && \
  git diff origin/main...HEAD > "$PATCH" && \
  if [ ! -s "$PATCH" ]; then echo 'No diff to review. Skipping Gemini.'; else \
    gemini --prompt "\nAct as a staff engineer.\nReview @${PATCH} against @docs/code-review-checklist.md.\nReturn GitHub-flavored markdown feedback.\n" --quiet > gemini_feedback.md; \
  fi                                         && \
  echo '🏗️   Building production bundle…'  && \
  npm run build
"""