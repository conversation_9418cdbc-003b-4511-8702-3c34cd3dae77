{"permissions": {"allow": ["<PERSON><PERSON>(tail:*)", "Bash(rg:*)", "Bash(npm run build:*)", "Bash(npm test:*)", "<PERSON><PERSON>(mv:*)", "Bash(find:*)", "Bash(npm run lint)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "WebFetch(domain:reactflow.dev)", "WebFetch(domain:github.com)", "Bash(npm install)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "mcp__ide__getDiagnostics", "WebFetch(domain:ai-sdk.dev)", "WebFetch(domain:vercel.com)", "WebFetch(domain:ai-sdk-reasoning.vercel.app)", "WebFetch(domain:sdk.vercel.ai)", "WebFetch(domain:www.assistant-ui.com)", "WebFetch(domain:www.assistant-ui.com)", "Bash(gemini:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(ls:*)", "Bash(npx prisma migrate dev:*)", "<PERSON><PERSON>(timeout:*)", "WebFetch(domain:docs.anthropic.com)"], "deny": []}, "hooks": {"PreToolUse": [{"hooks": [{"type": "command", "command": "python3 /Users/<USER>/Desktop/github/next13-clarify/remocode/v2/hooks/pretool_guard.py"}]}], "Notification": [{"hooks": [{"type": "command", "command": "python3 /Users/<USER>/Desktop/github/next13-clarify/remocode/v2/hooks/notification_handler.py \"$CLAUDE_NOTIFICATION_TYPE\" \"$CLAUDE_NOTIFICATION_MESSAGE\""}]}], "UserPromptSubmit": [{"hooks": [{"type": "command", "command": "python3 /Users/<USER>/Desktop/github/next13-clarify/remocode/v2/hooks/menu_detector.py \"$CLAUDE_USER_PROMPT\""}]}]}}