---

RemoCode v1.0: An Autonomous, Multi-Agent
Development Framework

Abstract

This document outlines the architecture and
implementation plan for RemoCode, a system designed
to manage one or more autonomous AI coding agents.
The primary objective is to create a
"fire-and-forget" development workflow where a human
engineer can delegate a coding task via a
specification file and be notified upon its
successful, quality-assured completion.

The system enables true asynchronous development,
allowing an engineer to kick off complex tasks and
disconnect (e.g., board a flight), while the agents
work, review each other's output, and handle common
roadblocks autonomously. The human remains the
ultimate authority, with the ability to monitor
progress via Telegram and, when necessary, "jump
into" a live, interactive terminal session to
provide direct guidance.

---

1. Core Principles & Rationale

The architecture of RemoCode is guided by four
principles:

1.  Asynchronous Control: The engineer's time is
    decoupled from the agent's work time. The primary
    user interface is Telegram, allowing for
    low-bandwidth, asynchronous monitoring and approval
    from any device.
2.  Hermetic Execution Environments: Each agent
    operates in a completely isolated workspace. This
    prevents parallel agents from interfering with each
    other's code, dependencies, or running processes,
    enabling a true multi-agent "team" workflow.
3.  Verifiable & Convergent Quality: The system must
    prevent "endless loops" of mediocre AI feedback.
    Quality is enforced through a structured,
    multi-stage "review gauntlet" where code must pass
    specific, objective checks, not just open-ended
    reviews. The process is designed to converge
    towards a "PASS" state or fail explicitly.
4.  Human as the Ultimate Authority: While the system
    is highly autonomous, the engineer can intervene at
    any point. The architecture provides a seamless
    "jump-in" capability, allowing a human to take over
    an agent's live terminal, review its state,
    provide input, and then hand control back.

---

2. System Architecture

The system is composed of several key components
that work in concert. The primary orchestrator is a
manager script that bootstraps and supervises
individual remocode agent sessions.

    1 +---------------------------------+
    2 |      Engineer (via Telegram)    |
    3 +-----------------+---------------+
    4                   ^
    5                   | (Notifications,
      Approval Prompts)
    6                   v
    7 +-----------------+---------------+
      +--------------------------------+
    8 |      RemoCode Manager Script    |----->|
      tmux (Session Manager)    |
    9 |  (start-agent.sh / API)         |
      +--------------------------------+

10 +---------------------------------+
|
11 | (Creates & Manages)
| (Hosts Persistent Sessions)
12 v
v
13 +-----------------------------------------
----------------------------------------+
14 | Isolated
Workspaces |
15 |
|
16 | +-------------------------+
+-------------------------+
+------------------+
17 | | Workspace A (Branch A) | |
Workspace B (Branch B) | | ...
|
18 | |-------------------------|
|-------------------------|
|------------------|
19 | | > git checkout branch-a | | > git
checkout branch-b | | |
20 | | > npm install | | > npm
install | | |
21 | | > remocode run spec-a | | >
remocode run spec-b | |
|
22 | | (Inside tmux session A) | (Inside
tmux session B) | | |
23 | +-------------------------+
+-------------------------+
+------------------+
24 |
|
25 +-----------------------------------------
----------------------------------------+

---

3. Phased Implementation Roadmap

This project will be implemented in four distinct,
sequential phases. Each phase builds upon the last,
progressively increasing the system's capability and
autonomy.

Phase 1: The Local Dual-Control Agent

- Objective: Create a single, reliable agent that can
  be controlled interactively from both the local
  terminal and Telegram on a single machine. This is
  the foundational engine.
- Rationale: Before scaling, the core interaction
  model must be perfected. This phase solves the
  complex problem of creating a seamless bridge
  between a TUI application (claude-code), a local
  terminal, and a remote API (Telegram).
- Key Tasks:
  1.  `remocode` Core Script: Develop the main Python
      application using asyncio.
  2.  PTY Wrapper (`ClaudeWrapper`): Implement the
      pseudo-terminal (PTY) logic to run claude-code
      in its full interactive mode. This is critical
      for capturing rich output.
  3.  Raw Terminal Mode: Implement tty and termios
      logic to switch the local stdin to raw mode,
      allowing for transparent forwarding of
      keystrokes to the agent.
  4.  Telegram Bot Integration: Create a
      TelegramBotManager that handles sending batched
      output and receiving commands/menu choices.
  5.  Dual-Input Logic: Implement the "first reply
      wins" mechanism, where input from either the
      terminal or a Telegram button press is
      accepted, and the other is discarded.
- Outcome: A developer can run ./remocode/remocode run
  <spec> and interact with the agent from their
  keyboard or phone interchangeably.

Phase 2: The Local Multi-Agent "Team"

- Objective: Enable the parallel execution of
  multiple, isolated agents on a single local machine.
- Rationale: This phase introduces the concept of
  workspace isolation and session management, which is
  the prerequisite for scaling the "AI team."
- Key Tasks:
  1.  `start-agent.sh` Manager Script: Create the
      bootstrapper script.
      - It will accept a branch-name and spec-file
        as arguments.
      - It will create a new directory under
        ~/coding-agents/<branch-name>.
      - It will git clone the project into the new
        directory and git checkout -b <branch-name>.
  2.  `tmux` Integration: The manager script will
      launch each remocode instance inside a new,
      named, detached tmux session (e.g., tmux new -d
      -s <branch-name> ...).
  3.  Dynamic `tmux` Attachment: The engineer can now
      list (tmux ls) and attach (tmux attach -t
      <branch-name>) to any running agent's live
      terminal.
- Outcome: A developer can run ./start-agent.sh
  multiple times to kick off several agents, each
  working on a different feature in its own directory
  and branch, without interference.

Phase 3: The Remote Singleton Agent

- Objective: Move a single agent from the local
  machine to a 24/7 remote server, enabling the "work
  while you fly" scenario.
- Rationale: This proves the system's ability to run
  headlessly and validates the remote access and
  authentication model.
- Key Tasks:
  1.  Server Provisioning: Set up a small Linux VPS
      (e.g., on Azure, using credits).
  2.  Environment Replication: Clone the project and
      set up the necessary environment (node, npm,
      uv, etc.) on the server.
  3.  Authentication Transfer:
      - Locate the claude-code session/token file(s)
        on the local machine.
      - Securely copy these files (via scp) to the
        exact same path on the remote server. This
        authenticates the server without needing a
        browser.
  4.  Remote "Jump-In" Mechanism: Install a web-based
      terminal tool like gotty. The agent is
      launched via gotty, providing a secure URL that
      allows the engineer to access the live tmux
      session from any web browser.
- Outcome: The agent runs entirely on the remote
  server. The engineer can shut down their local
  machine, interact solely via Telegram, and still
  have a "break-glass" option to access the live
  terminal via a web link.

Phase 4: The Remote AI Development Team & Quality
Gauntlet

- Objective: Achieve the final vision: a fully
  autonomous, multi-agent team running on a remote
  server, complete with automated quality assurance
  and live web previews.
- Rationale: This phase combines the parallelism of
  Phase 2 with the remote operation of Phase 3, and
  adds the critical layer of automated quality
  control.
- Key Tasks:
  1.  Reverse Proxy for Previews: Set up a reverse
      proxy like Caddy. The start-agent.sh script
      will be updated to assign a unique port to each
      agent's npm run dev server and use Caddy's API
      to dynamically create a public subdomain for
      it (e.g., https://feature-x.your-domain.com).
  2.  The Automated Review Gauntlet
      (`run-review.sh`):
      - This script is triggered after the primary
        coding agent finishes.
      - It uses a framework like LangGraph to
        orchestrate multiple "reviewer" agents in
        parallel (Spec Compliance, Security, Best
        Practices).
      - Reviewers output structured feedback,
        culminating in a single <<<PASS>>> or
        <<<REWORK>>> keyword.
  3.  Automated Quality Gates: If the review verdict
      is <<<PASS>>>, the script proceeds to run npm
      run test and npm run build. The process only
      succeeds if these gates pass.
  4.  Automated Reporting: Upon successful
      completion, a final script can use a headless
      browser tool like Playwright to take a
      screenshot of the live preview URL and send it
      to Telegram.
- Outcome: A fully automated pipeline. An engineer
  delegates a task and receives a Telegram message
  hours or days later containing a screenshot of the
  finished, tested, and built feature, a link to the
  live preview, and a link to the pull request.

---

4. Trade-offs & FAQ

- Q: Why `tmux` over a simpler process manager like
  `pm2`?
  - A: tmux provides the critical ability to attach
    to the running process's interactive terminal.
    pm2 is excellent for managing daemons but
    lacks this direct, stateful intervention
    capability, which is core to the "human as the
    ultimate authority" principle.

- Q: How do we prevent endless review loops?
  - A: By enforcing a strict output format from the
    review agents. The final review must conclude
    with either <<<PASS>>> or <<<REWORK>>>. The
    loop only continues on <<<REWORK>>>.
    Furthermore, a "circuit-breaker" (e.g., max 3
    review cycles) will be implemented to escalate
    to a human if the agent gets stuck.

- Q: Is a 24/7 server cost-effective?
  - A: Yes, if it maximizes the value of a paid AI
    subscription. A server can be provisioned for a
    modest monthly cost. Its value is in ensuring
    the AI agent's session state is preserved and
    it can work during times the engineer is
    offline, effectively preventing wasted
    subscription time.

- Q: What about dynamic scaling?
  - A: For 3-4 agents, a single, fixed-size VPS is
    the most practical solution. True dynamic
    scaling (adding/removing server nodes on the
    fly) is a significant architectural leap. This
    would involve containerizing the entire
    application with Docker (using Volumes for
    state persistence) and managing it with an
    orchestrator like Kubernetes. This should be
    considered a post-v1.0 enhancement.
