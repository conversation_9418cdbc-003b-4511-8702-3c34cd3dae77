# Code Review Checklist

Use this checklist when reviewing pull requests with Gemini CLI or during manual code reviews. Each item should be verified before merging changes.

- [ ] **TypeScript**: No type errors. Prefer `type` over `interface` when possible.
- [ ] **Tests**: All unit and e2e tests pass (`npm run test:all`). Adequate test coverage for new code.
- [ ] **Performance**: Consider performance optimizations. Avoid unnecessary re-renders, large payloads, or inefficient DB queries.
- [ ] **Error Handling**: Proper try/catch blocks, graceful error messages, and logging.
- [ ] **Loading & Empty States**: Meaningful UI states for loading, error, and empty data scenarios.
- [ ] **Accessibility**: Semantic HTML, ARIA attributes, keyboard navigation, color contrast.
- [ ] **Code Style**: Follows ESLint + Prettier rules, descriptive variable/function names, DRY principles.
- [ ] **Comments & Docs**: Key functions, components, and complex logic well-documented. Update route-level README if needed.
- [ ] **Feature Flags**: Use centralized flag enum/object. Minimize scattered checks.
- [ ] **Database**: Fields follow snake_case, include `created_at` & `updated_at`, use human-readable ID prefixes.
- [ ] **Security**: No hard-coded secrets, safe user input handling, least privilege on API calls.
