#!/usr/bin/env python3
"""
menu_coordinator.py - Coordinate menu responses between hooks and Telegram
Handles the "first to win" coordination for hook-based menu detection
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from typing import Dict, Optional, Any
import urllib.request
import urllib.parse

# Add the parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

TG_TOKEN = os.getenv("TG_TOKEN")
TG_CHAT = os.getenv("TG_CHAT")


class MenuCoordinator:
    """Coordinate menu responses between terminal and Telegram"""
    
    def __init__(self):
        self.menu_state_file = Path(".remocode_menu.json")
        self.notification_state_file = Path(".remocode_notification.json")
        self.response_file = Path(".remocode_response.json")
        
    def wait_for_response(self, timeout: float = 300.0) -> Optional[str]:
        """
        Wait for user response from either terminal or Telegram
        
        Args:
            timeout: Maximum wait time in seconds
            
        Returns:
            User response string or None if timeout
        """
        import time
        
        start_time = time.time()
        last_update_id: Optional[int] = None
        
        while time.time() - start_time < timeout:
            # Check for terminal input (non-blocking)
            terminal_response = self._check_terminal_input()
            if terminal_response:
                self._write_response(terminal_response, "terminal")
                return terminal_response
            
            # Check for Telegram response
            telegram_response = self._check_telegram_updates(last_update_id)
            if telegram_response:
                response, last_update_id = telegram_response
                self._write_response(response, "telegram")
                return response
            
            # Small delay to prevent busy waiting
            time.sleep(0.1)
        
        return None
    
    def _check_terminal_input(self) -> Optional[str]:
        """Check for terminal input (non-blocking) - DISABLED to avoid conflicts with Claude Code"""
        # NOTE: We don't read from terminal stdin anymore to avoid interfering 
        # with Claude Code's own input handling. The user should interact with
        # Claude Code normally or use Telegram for menu responses.
        return None
    
    async def wait_for_telegram_response_only(self, timeout: float = 300.0) -> Optional[str]:
        """
        Wait for Telegram response only, don't interfere with terminal input
        
        Args:
            timeout: Maximum wait time in seconds
            
        Returns:
            Telegram response string or None if timeout/no response
        """
        import time
        
        start_time = time.time()
        last_update_id: Optional[int] = None
        
        while time.time() - start_time < timeout:
            # Only check for Telegram response
            telegram_response = self._check_telegram_updates(last_update_id)
            if telegram_response:
                response, last_update_id = telegram_response
                self._write_response(response, "telegram")
                return response
            
            # Small delay to prevent busy waiting
            await asyncio.sleep(0.5)
        
        return None
    
    def _check_telegram_updates(self, last_update_id: Optional[int]) -> Optional[tuple[str, int]]:
        """Check for Telegram callback responses"""
        if not TG_TOKEN or not TG_CHAT:
            return None
        
        try:
            # Get updates from Telegram
            updates = self._telegram_request(
                "getUpdates",
                offset=(last_update_id or 0) + 1,
                timeout=1  # Short timeout for non-blocking
            )["result"]
            
            # Find callback query responses
            for update in updates:
                update_id = update["update_id"]
                
                if "callback_query" in update:
                    callback = update["callback_query"]
                    response = callback["data"]
                    
                    # Acknowledge the callback
                    self._telegram_request(
                        "answerCallbackQuery", 
                        callback_query_id=callback["id"]
                    )
                    
                    # Update the original message
                    if "message" in callback:
                        msg_id = callback["message"]["message_id"]
                        self._telegram_request(
                            "editMessageText",
                            chat_id=TG_CHAT,
                            message_id=msg_id,
                            text=f"{callback['message']['text']}\n\n*Selected:* {response}",
                            parse_mode="Markdown"
                        )
                    
                    return response, update_id
            
            # Return the last update ID even if no callback found
            if updates:
                return None, updates[-1]["update_id"]
            
        except Exception as e:
            print(f"Error checking Telegram updates: {e}", file=sys.stderr)
        
        return None
    
    def _telegram_request(self, method: str, **params) -> Dict[str, Any]:
        """Make a Telegram API request"""
        url = f"https://api.telegram.org/bot{TG_TOKEN}/{method}"
        data = json.dumps(params).encode()
        req = urllib.request.Request(
            url, 
            data=data, 
            headers={"Content-Type": "application/json"}
        )
        
        with urllib.request.urlopen(req, timeout=10) as resp:
            return json.loads(resp.read().decode())
    
    def _write_response(self, response: str, source: str) -> None:
        """Write the response to a file for coordination"""
        response_data = {
            "response": response,
            "source": source,
            "timestamp": str(Path().cwd() / ".remocode_response.json")
        }
        
        try:
            with open(self.response_file, "w") as f:
                json.dump(response_data, f, indent=2)
        except Exception as e:
            print(f"Error writing response: {e}", file=sys.stderr)
    
    def load_menu_state(self) -> Optional[Dict]:
        """Load current menu state from file"""
        try:
            if self.menu_state_file.exists():
                with open(self.menu_state_file) as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading menu state: {e}", file=sys.stderr)
        return None
    
    def load_notification_state(self) -> Optional[Dict]:
        """Load current notification state from file"""
        try:
            if self.notification_state_file.exists():
                with open(self.notification_state_file) as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading notification state: {e}", file=sys.stderr)
        return None
    
    def clear_state_files(self) -> None:
        """Clear all state files after processing"""
        for file_path in [self.menu_state_file, self.notification_state_file, self.response_file]:
            try:
                if file_path.exists():
                    file_path.unlink()
            except Exception as e:
                print(f"Error clearing {file_path}: {e}", file=sys.stderr)


def main():
    """Main entry point for testing the coordinator"""
    coordinator = MenuCoordinator()
    
    print("Waiting for user response...")
    response = coordinator.wait_for_response(timeout=60.0)
    
    if response:
        print(f"Received response: {response}")
    else:
        print("No response received (timeout)")
    
    coordinator.clear_state_files()


if __name__ == "__main__":
    main()