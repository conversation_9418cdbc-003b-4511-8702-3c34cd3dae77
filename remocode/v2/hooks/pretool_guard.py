#!/usr/bin/env python3
"""
pretool_guard.py – RemoCode v2 hook

Called by <PERSON> *before* each tool invocation.
Decides automatically or asks user via Telegram.
"""

from __future__ import annotations

import json
import os
import re
import sys
import time
from pathlib import Path
from typing import Any, Dict, List

import urllib.request
import urllib.parse

TG_TOKEN = os.getenv("TG_TOKEN")
TG_CHAT = os.getenv("TG_CHAT")

# ---------------------------------------------------------------------------
# Heuristics
# ---------------------------------------------------------------------------

# Auto-approve these tools - they're safe and commonly used
AUTONOMOUS_TOOLS: set[str] = {
    # File operations
    "Read", "Write", "Edit", "MultiEdit", "NotebookRead", "NotebookEdit",
    
    # Search and navigation
    "grep_search", "codebase_search", "file_search", "list_dir", 
    "Glob", "glob", "LS", "ls", "Task", "Search", "Grep",
    
    # Web and API (usually safe)
    "WebFetch", "WebSearch",
    
    # Development tools
    "Bash",  # Checked by regex patterns below
    
    # Any tool starting with these prefixes (for extensibility)
}

# Auto-approve these bash commands - safe and commonly used
SAFE_BASH_PATTERNS = [
    # NPM/Node operations
    r"^npm (run|install|ci|test|build|start|dev).*",
    r"^yarn (install|build|test|start|dev).*",
    r"^pnpm (install|build|test|start|dev).*",
    r"^node .*",
    r"^npx (?!create-).*",  # Allow npx except create commands
    
    # Git operations (read-only)
    r"^git (status|diff|log|show|branch|checkout|pull|fetch|add|commit|stash).*",
    
    # File operations
    r"^ls.*", r"^cat.*", r"^head.*", r"^tail.*", r"^find.*", r"^grep.*",
    r"^mkdir.*", r"^cp.*", r"^mv.*", r"^touch.*", r"^chmod.*",
    
    # Development tools
    r"^python.*", r"^pip.*", r"^pytest.*", r"^black.*", r"^ruff.*",
    r"^cargo.*", r"^rustc.*", r"^go (run|build|test).*",
    r"^make.*", r"^cmake.*",
    
    # System info (safe)
    r"^ps.*", r"^top.*", r"^htop.*", r"^df.*", r"^du.*", r"^free.*",
    r"^uname.*", r"^whoami.*", r"^pwd.*", r"^which.*", r"^where.*",
    
    # Text processing
    r"^awk.*", r"^sed.*", r"^sort.*", r"^uniq.*", r"^wc.*",
    
    # Docker (read-only operations)
    r"^docker (ps|images|inspect|logs).*",
    r"^docker-compose (ps|logs).*",
]

# Only ask for permission on these - genuinely dangerous operations
DANGEROUS_BASH_PATTERNS = [
    # Destructive operations
    r"^rm -rf.*", r"^rm -f.*", r"^rmdir.*",
    
    # System operations
    r"^sudo .*", r"^su .*",
    
    # Network/deployment
    r"^git push.*", r"^vercel --prod.*", r"^netlify deploy --prod.*",
    r"^aws .*", r"^gcloud .*", r"^kubectl.*",
    
    # Package management (potentially dangerous)
    r"^npm (publish|unpublish).*",
    r"^pip install -e.*",  # Editable installs
    
    # Docker operations that change state
    r"^docker (run|exec|build|push|pull|stop|kill|rm).*",
    r"^docker-compose (up|down|build).*",
    
    # Process control
    r"^kill.*", r"^killall.*", r"^pkill.*",
    
    # File system changes
    r"^mount.*", r"^umount.*", r"^fdisk.*",
]


def _matches(cmd: str, patterns: List[str]) -> bool:
    return any(re.match(p, cmd) for p in patterns)


def _format_tool_description(tool: str, tool_input: dict) -> str:
    """Format tool description with meaningful details"""
    # Handle None or empty tool names
    if not tool or tool == "None":
        tool = "Unknown Tool"
    
    # Common tool descriptions
    tool_descriptions = {
        "Task": "🎯 Execute a sub-task or agent",
        "ExitPlanMode": "📋 Exit planning mode and proceed",
        "TodoWrite": "📝 Update todo list",
        "WebFetch": "🌐 Fetch content from web",
        "WebSearch": "🔍 Search the web",
        "NotebookRead": "📓 Read Jupyter notebook",
        "NotebookEdit": "📓 Edit Jupyter notebook",
        "Read": "📖 Read file content",
        "Write": "✏️ Write file content", 
        "Edit": "✏️ Edit file content",
        "MultiEdit": "✏️ Edit multiple files",
        "Glob": "🔍 Find files by pattern",
        "Grep": "🔍 Search text in files",
        "LS": "📁 List directory contents",
        "Bash": "⚡ Run terminal command",
    }
    
    # Get tool description
    tool_desc = tool_descriptions.get(tool, f"🔧 Use {tool} tool")
    
    # Format tool input details
    if not tool_input or tool_input == {} or tool_input is None:
        input_details = "*(no parameters provided)*"
    else:
        # Show key parameters in a readable way
        key_params = []
        for key, value in tool_input.items():
            if value is None:
                key_params.append(f"**{key}**: `None`")
            elif isinstance(value, str) and len(value) > 100:
                # Truncate long strings
                key_params.append(f"**{key}**: `{value[:100]}...`")
            elif isinstance(value, (list, dict)) and len(str(value)) > 200:
                # Summarize complex objects
                key_params.append(f"**{key}**: `{type(value).__name__}({len(value)} items)`")
            else:
                key_params.append(f"**{key}**: `{value}`")
        
        if key_params:
            input_details = "\n".join(key_params)
        else:
            input_details = "*(empty parameters)*"
    
    return f"{tool_desc}\n\n{input_details}\n\nAllow this tool to run?"


# ---------------------------------------------------------------------------
# Telegram helpers
# ---------------------------------------------------------------------------

def _telegram(method: str, **params) -> Dict[str, Any]:
    """Lightweight POST using stdlib to avoid external deps."""
    url = f"https://api.telegram.org/bot{TG_TOKEN}/{method}"
    data = json.dumps(params).encode()
    req = urllib.request.Request(url, data=data, headers={"Content-Type": "application/json"})
    with urllib.request.urlopen(req, timeout=10) as resp:
        return json.loads(resp.read().decode())


def _ask_user(message: str, tool: str = "", cmd: str = "") -> bool:
    """Send inline-keyboard prompt and wait for response with rich options."""
    if not TG_TOKEN or not TG_CHAT:
        return False  # Telegram not configured → deny

    # Create richer keyboard based on context
    if "git push" in cmd:
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Push now", "callback_data": "yes"},
                    {"text": "📝 Review first", "callback_data": "no"},
                ],
                [
                    {"text": "🔒 Always allow git push", "callback_data": "always_allow"},
                    {"text": "⛔️ Cancel", "callback_data": "no"},
                ]
            ]
        }
    elif "rm -rf" in cmd or "sudo" in cmd:
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "⚠️ Yes, I understand the risk", "callback_data": "yes"},
                    {"text": "⛔️ No, cancel", "callback_data": "no"},
                ],
                [
                    {"text": "📋 Show me the full command first", "callback_data": "show_details"},
                ]
            ]
        }
    elif "docker" in cmd:
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "🐳 Allow Docker command", "callback_data": "yes"},
                    {"text": "⛔️ Deny", "callback_data": "no"},
                ],
                [
                    {"text": "🔒 Always allow Docker", "callback_data": "always_allow"},
                ]
            ]
        }
    else:
        # Default keyboard for other cases
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Allow", "callback_data": "yes"},
                    {"text": "⛔️ Deny", "callback_data": "no"},
                ],
                [
                    {"text": "🔒 Always allow this tool", "callback_data": "always_allow"},
                ]
            ]
        }

    sent = _telegram(
        "sendMessage",
        chat_id=TG_CHAT,
        text=f"🤖 *Claude needs permission*\n{message}",
        parse_mode="Markdown",
        reply_markup=keyboard,
    )
    msg_id = sent["result"]["message_id"]

    last_update_id: int | None = None
    start = time.time()
    while time.time() - start < 300:  # 5 min
        updates = _telegram(
            "getUpdates",
            offset=(last_update_id or 0) + 1,
            timeout=60,
        )["result"]
        for upd in updates:
            last_update_id = upd["update_id"]
            if "callback_query" in upd and upd["callback_query"]["message"]["message_id"] == msg_id:
                choice = upd["callback_query"]["data"]
                _telegram("answerCallbackQuery", callback_query_id=upd["callback_query"]["id"])
                approved = choice in ["yes", "always_allow"]
                _telegram(
                    "editMessageText",
                    chat_id=TG_CHAT,
                    message_id=msg_id,
                    text=f"{message}\n\n*Choice:* {'✅ Allowed' if approved else '⛔️ Denied'}",
                    parse_mode="Markdown",
                )
                return approved
    # timeout
    _telegram(
        "editMessageText",
        chat_id=TG_CHAT,
        message_id=msg_id,
        text=f"{message}\n\n*Choice:* ⏰ Timed out (denied)",
        parse_mode="Markdown",
    )
    return False


# ---------------------------------------------------------------------------
# Main
# ---------------------------------------------------------------------------

def main() -> None:
    payload = json.loads(sys.stdin.read())
    tool = payload.get("tool") or "Unknown"
    tool_input: dict = payload.get("input", {})
    human_msg = payload.get("message", "")
    
    # Debug logging to file for troubleshooting
    debug_info = f"Tool: {tool}, Input: {tool_input}, Message: {human_msg}\n"
    with open("/Users/<USER>/Desktop/github/next13-clarify/remocode.log", "a") as f:
        f.write(f"[pretool_guard] {debug_info}")

    # Fast path – auto-approve common tools
    if tool in AUTONOMOUS_TOOLS:
        if tool == "Bash":
            cmd = tool_input.get("command", "")
            
            # Check dangerous patterns first
            if _matches(cmd, DANGEROUS_BASH_PATTERNS):
                # Only ask for genuinely dangerous commands
                description = f"⚠️ **Potentially dangerous bash command:**\n\n**command**: `{cmd}`\n\nThis command could modify system state or delete data. Allow execution?"
                approved = _ask_user(description, tool, cmd)
                if approved:
                    print(json.dumps({"decision": "approve"}))
                else:
                    print(json.dumps({"decision": "block", "reason": "User denied dangerous command"}))
                return
            elif _matches(cmd, SAFE_BASH_PATTERNS):
                # Auto-approve safe commands
                print(json.dumps({"decision": "approve"}))
                return
            else:
                # Unknown bash command - be cautious but not overly strict
                if len(cmd) < 100 and not any(danger in cmd.lower() for danger in ['rm', 'delete', 'drop', 'truncate']):
                    # Short commands without dangerous keywords - probably safe
                    print(json.dumps({"decision": "approve"}))
                    return
                else:
                    # Longer or potentially dangerous - ask
                    description = f"🤔 **Unknown bash command:**\n\n**command**: `{cmd}`\n\nShould I run this command?"
                    approved = _ask_user(description, tool, cmd)
                    if approved:
                        print(json.dumps({"decision": "approve"}))
                    else:
                        print(json.dumps({"decision": "block", "reason": "User denied unknown command"}))
                    return
        else:
            # Non-bash tools in autonomous list - auto approve
            print(json.dumps({"decision": "approve"}))
            return

    # Tool not in autonomous list - ask for approval  
    description = human_msg or _format_tool_description(tool, tool_input)
    
    # Add raw JSON display option for debugging
    description += f"\n\n**Raw JSON payload for debugging:**\n```json\n{json.dumps(payload, indent=2)}\n```"
    
    approved = _ask_user(description, tool)
    if approved:
        print(json.dumps({"decision": "approve"}))
    else:
        print(json.dumps({"decision": "block", "reason": "User denied tool usage"}))


if __name__ == "__main__":
    try:
        main()
    except Exception as exc:  # pragma: no cover
        print(json.dumps({"approved": False, "error": str(exc)}))