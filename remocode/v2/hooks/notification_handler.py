#!/usr/bin/env python3
"""
notification_handler.py - <PERSON>le Claude Code notification events
Processes notification events when <PERSON> waits for user input
"""

import json
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict, Optional

import urllib.request
import urllib.parse

TG_TOKEN = os.getenv("TG_TOKEN")
TG_CHAT = os.getenv("TG_CHAT")


def _telegram(method: str, **params) -> Dict[str, Any]:
    """Lightweight POST using stdlib to avoid external deps."""
    url = f"https://api.telegram.org/bot{TG_TOKEN}/{method}"
    data = json.dumps(params).encode()
    req = urllib.request.Request(url, data=data, headers={"Content-Type": "application/json"})
    with urllib.request.urlopen(req, timeout=10) as resp:
        return json.loads(resp.read().decode())


def _send_telegram_menu(message: str, keyboard: Dict) -> bool:
    """Send menu to Telegram and wait for response"""
    if not TG_TOKEN or not TG_CHAT:
        return False
    
    try:
        sent = _telegram(
            "sendMessage",
            chat_id=TG_CHAT,
            text=message,
            parse_mode="Markdown",
            reply_markup=keyboard,
        )
        msg_id = sent["result"]["message_id"]
        
        # Wait for callback response
        last_update_id: int | None = None
        start = time.time()
        while time.time() - start < 300:  # 5 min
            updates = _telegram(
                "getUpdates",
                offset=(last_update_id or 0) + 1,
                timeout=60,
            )["result"]
            for upd in updates:
                last_update_id = upd["update_id"]
                if "callback_query" in upd and upd["callback_query"]["message"]["message_id"] == msg_id:
                    choice = upd["callback_query"]["data"]
                    _telegram("answerCallbackQuery", callback_query_id=upd["callback_query"]["id"])
                    _telegram(
                        "editMessageText",
                        chat_id=TG_CHAT,
                        message_id=msg_id,
                        text=f"{message}\n\n*Choice:* ✅ Option {choice} selected",
                        parse_mode="Markdown",
                    )
                    
                    # Write response to file for Claude Code to read
                    with open(".remocode_response.txt", "w") as f:
                        f.write(choice)
                    
                    return True
        
        # Timeout
        _telegram(
            "editMessageText", 
            chat_id=TG_CHAT,
            message_id=msg_id,
            text=f"{message}\n\n*Choice:* ⏰ Timed out",
            parse_mode="Markdown",
        )
        return False
    except Exception as e:
        print(f"Telegram error: {e}", file=sys.stderr)
        return False


class NotificationHandler:
    """Handle Claude Code notification events"""
    
    def __init__(self):
        pass
    
    def handle_notification(self, notification_type: str, message: str) -> bool:
        """
        Handle different types of Claude Code notifications
        
        Args:
            notification_type: Type of notification (e.g., "tool_permission", "idle")
            message: Notification message content
        """
        try:
            # Write notification state for coordination
            self._write_notification_state(notification_type, message)
            
            if notification_type == "tool_permission":
                self._handle_tool_permission(message)
            elif notification_type == "idle":
                self._handle_idle_notification(message)
            elif self._is_plan_approval_menu(message):
                self._handle_plan_approval_menu(message)
            else:
                self._handle_generic_notification(notification_type, message)
            
            return True
        except Exception as e:
            print(f"Error handling notification: {e}", file=sys.stderr)
            return False
    
    def _handle_tool_permission(self, message: str) -> None:
        """Handle tool permission requests"""
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Allow", "callback_data": "allow"},
                    {"text": "⛔️ Deny", "callback_data": "deny"},
                ]
            ]
        }
        
        notification_text = f"🔒 Tool Permission Request:\n\n{message}\n\nAllow Claude to use this tool?"
        _send_telegram_menu(notification_text, keyboard)
    
    def _handle_idle_notification(self, message: str) -> None:
        """Handle idle timeout notifications"""
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "⏰ Continue", "callback_data": "continue"},
                    {"text": "🛑 Stop", "callback_data": "stop"},
                ]
            ]
        }
        
        notification_text = f"⏱️ Claude Code Idle:\n\n{message}\n\nWhat would you like to do?"
        _send_telegram_menu(notification_text, keyboard)
    
    def _is_plan_approval_menu(self, message: str) -> bool:
        """Check if message is a plan approval menu"""
        message_lower = message.lower()
        return any(phrase in message_lower for phrase in [
            "would you like to proceed",
            "ready to code",
            "auto-accept edits",
            "manually approve edits", 
            "keep planning",
            "here is claude's plan",
            "proceed with the plan"
        ])
    
    def _handle_plan_approval_menu(self, message: str) -> None:
        """Handle plan approval menu with proper options"""
        # Extract the plan content and options
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Yes, auto-accept edits", "callback_data": "1"}
                ],
                [
                    {"text": "🔍 Yes, manually approve edits", "callback_data": "2"}
                ],
                [
                    {"text": "📝 No, keep planning", "callback_data": "3"}
                ]
            ]
        }
        
        # Format the message nicely
        clean_message = message.strip()
        if len(clean_message) > 1000:
            clean_message = clean_message[:1000] + "...\n\n[Plan truncated for Telegram]"
        
        notification_text = f"🤖 **Claude Code Plan Approval**\n\n{clean_message}\n\n**Choose an option:**"
        _send_telegram_menu(notification_text, keyboard)
    
    def _handle_generic_notification(self, notification_type: str, message: str) -> None:
        """Handle other notification types"""
        try:
            # Improve message formatting
            if not message or message.strip() == "":
                formatted_message = f"🔔 Claude Code Notification\n\n**Type:** {notification_type}\n**Message:** *(empty)*"
            else:
                formatted_message = f"🔔 Claude Code Notification\n\n**Type:** {notification_type}\n**Message:** {message}"
            
            _telegram(
                "sendMessage",
                chat_id=TG_CHAT,
                text=formatted_message,
                parse_mode="Markdown"
            )
        except Exception as e:
            print(f"Failed to send generic notification: {e}", file=sys.stderr)
    
    def _write_notification_state(self, notification_type: str, message: str) -> None:
        """Write notification state to file"""
        state = {
            "event": "notification",
            "type": notification_type,
            "message": message,
            "timestamp": str(Path().cwd() / ".remocode_notification.json")
        }
        
        try:
            with open(".remocode_notification.json", "w") as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            print(f"Error writing notification state: {e}", file=sys.stderr)


def main():
    """Main entry point for the notification hook"""
    try:
        # Debug: log all arguments received
        with open("/Users/<USER>/Desktop/github/next13-clarify/remocode.log", "a") as f:
            f.write(f"[notification_handler] Args received: {sys.argv}\n")
            f.write(f"[notification_handler] Env vars: CLAUDE_NOTIFICATION_TYPE='{os.getenv('CLAUDE_NOTIFICATION_TYPE', 'NOT_SET')}', CLAUDE_NOTIFICATION_MESSAGE='{os.getenv('CLAUDE_NOTIFICATION_MESSAGE', 'NOT_SET')}'\n")
        
        if len(sys.argv) < 2:
            print("Usage: notification_handler.py <type> [message]", file=sys.stderr)
            sys.exit(1)
        
        notification_type = sys.argv[1] if len(sys.argv) > 1 else "unknown"
        message = sys.argv[2] if len(sys.argv) > 2 else ""
        
        # Debug logging to file for troubleshooting
        debug_info = f"Type: '{notification_type}', Message: '{message}'\n"
        with open("/Users/<USER>/Desktop/github/next13-clarify/remocode.log", "a") as f:
            f.write(f"[notification_handler] Processed: {debug_info}")
        
        # If no message provided, skip processing but still log it
        if not message or message.strip() == "":
            print(f"Notification type '{notification_type}' received with empty message - skipping")
            return
        
        handler = NotificationHandler()
        
        # Handle the notification
        success = handler.handle_notification(notification_type, message)
        
        if success:
            print(f"Notification handled: {notification_type}")
        else:
            print(f"Failed to handle notification: {notification_type}", file=sys.stderr)
            sys.exit(1)
    
    except Exception as e:
        with open("/Users/<USER>/Desktop/github/next13-clarify/remocode.log", "a") as f:
            f.write(f"[notification_handler] ERROR: {e}\n")
        print(f"Notification handler error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()