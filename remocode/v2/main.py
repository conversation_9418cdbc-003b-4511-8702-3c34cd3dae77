#!/usr/bin/env python3
"""Entry point for RemoCode v2 (hook-based)."""

from __future__ import annotations

import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import List

import typer
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from remocode.src.config import config_manager  # type: ignore
import json

# ---------------------------------------------------------------------------
# Helpers to configure Claude <PERSON> hooks via settings.local.json
# ---------------------------------------------------------------------------


def _ensure_hook_config(hook_script: Path, silent: bool = False) -> None:
    """Add our PreToolUse hook to .claude/settings.local.json (project-level)."""

    settings_dir = Path(".claude")
    settings_dir.mkdir(exist_ok=True)
    settings_file = settings_dir / "settings.local.json"

    config: dict = {}
    if settings_file.exists():
        try:
            config = json.loads(settings_file.read_text())
        except json.JSONDecodeError:
            if not silent:
                console.print("[yellow]⚠️  Couldn't parse existing settings.local.json, replacing…[/]")

    hooks_section = config.setdefault("hooks", {})
    pre_tool_hooks: list = hooks_section.setdefault("PreToolUse", [])

    # Ensure our command is present (matcher empty → all tools)
    command_str = f"python3 {hook_script.resolve()}"
    found = False
    for matcher in pre_tool_hooks:
        for h in matcher.get("hooks", []):
            if h.get("command") == command_str:
                found = True
                break
    if not found:
        pre_tool_hooks.append({
            "hooks": [
                {
                    "type": "command",
                    "command": command_str,
                }
            ]
        })

    settings_file.write_text(json.dumps(config, indent=2))
    if not silent:
        console.print(f"[green]✓ Hook configuration ensured at {settings_file}[/]")

from remocode.src.telegram_bot import TelegramBotManager  # type: ignore
from .claude_streamer import ClaudeStreamer
from .hooks.menu_coordinator import MenuCoordinator

console = Console(file=sys.stderr)
app = typer.Typer(add_completion=False, help="RemoCode v2 – hook-driven runner")


def _setup_logging(level: str = "INFO", silent: bool = False) -> None:
    """Setup logging with optional silent mode for complete transparency"""
    handlers = []
    
    # Always add file handler for debugging
    file_handler = logging.FileHandler(Path(__file__).parent.parent / "remocode.log")
    file_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
    handlers.append(file_handler)
    
    # Only add console handler if not in silent mode
    if not silent:
        console_handler = RichHandler(console=console, show_path=False, level=level)
        handlers.append(console_handler)
    
    logging.basicConfig(
        level=getattr(logging, level.upper(), logging.INFO),
        format="%(message)s",
        handlers=handlers,
        force=True  # Override any existing config
    )


@app.command("run")
def run(
    log_level: str = typer.Option("INFO", help="Logging level"),
    silent: bool = typer.Option(True, "--silent/--verbose", help="Silent mode - no console output for transparency (default)"),
    plan_mode: bool = typer.Option(False, "--plan", help="Start Claude Code in plan mode"),
    hook_path: Path = typer.Option(
        Path(__file__).parent / "hooks" / "pretool_guard.py",
        help="Path to pre-tool-use hook",
    ),
    cli_args: List[str] = typer.Argument(None, help="Arguments forwarded to claude-code"),
) -> None:
    """Launch Claude-Code with streaming + hook infrastructure."""
    _setup_logging(log_level, silent=silent)
    env = config_manager.env

    async def _async_main() -> None:
        # 0. Check for existing instance
        lock_file = Path("runtime/.remocode_v2.lock")
        lock_file.parent.mkdir(exist_ok=True)
        
        if lock_file.exists():
            if not silent:
                console.print("[yellow]⚠️  Another RemoCode v2 instance may be running. Continuing anyway...[/]")
        
        # Create lock file
        import os
        lock_file.write_text(str(os.getpid()))
        
        try:
            # 1. Ensure hook configuration so Claude Code can invoke our script
            _ensure_hook_config(hook_path, silent=silent)

            # Telegram output mirror
            tg_mgr = TelegramBotManager(env)
            await tg_mgr.start()

            # Menu coordinator for hook-based menu detection
            menu_coordinator = MenuCoordinator()

            # Claude streamer
            import errno, sys

            async def _out(line: str) -> None:
                # Forward output to terminal for normal Claude Code display
                # Use print() instead of direct os.write() to avoid terminal interference
                try:
                    # Use print with flush to ensure immediate output without buffering
                    print(line, end='', flush=True)
                except Exception:
                    pass  # swallow any stdout write errors
                # Also forward to Telegram for remote monitoring
                await tg_mgr.send_output(line, silent=True)

            # Add plan mode flag if requested
            final_cli_args = cli_args or []
            if plan_mode and "--permission-mode" not in " ".join(final_cli_args):
                final_cli_args = ["--permission-mode", "plan"] + final_cli_args
            
            # For transparent mode, run Claude Code as subprocess without PTY interception
            # This preserves terminal control while still allowing background monitoring
            import subprocess
            
            # Run Claude Code directly, inheriting stdin/stdout/stderr
            cmd = env.claude_cmd.split() + final_cli_args
            
            proc = subprocess.Popen(
                cmd,
                stdin=sys.stdin,
                stdout=sys.stdout,
                stderr=sys.stderr,
                # Don't use PTY - let Claude Code handle terminal directly
            )

            # Background task to monitor menu events and coordinate responses
            async def _monitor_menu_events():
                """Monitor for menu events and coordinate responses between terminal and Telegram"""
                while proc.poll() is None:  # While Claude Code is running
                    try:
                        # Check if there's a menu waiting for response
                        menu_state = menu_coordinator.load_menu_state()
                        notification_state = menu_coordinator.load_notification_state()
                        
                        if menu_state or notification_state:
                            if not silent:
                                logger.info("Menu/notification detected, waiting for Telegram response...")
                            
                            # Only wait for Telegram responses, let Claude Code handle terminal input normally
                            response = await menu_coordinator.wait_for_telegram_response_only(timeout=30.0)
                            
                            if response:
                                if not silent:
                                    logger.info(f"Telegram response received: {response}")
                                
                                # Send the response to Claude Code via stdin
                                try:
                                    proc.stdin.write(f"{response}\n".encode())
                                    proc.stdin.flush()
                                except Exception as e:
                                    logger.error(f"Failed to send response to Claude: {e}")
                                
                                # Clear state files
                                menu_coordinator.clear_state_files()
                            else:
                                # No Telegram response - let Claude Code handle it normally
                                # Just clear the state files
                                menu_coordinator.clear_state_files()
                        
                        # Small delay to prevent busy waiting
                        await asyncio.sleep(1.0)
                        
                    except Exception as e:
                        logger.error(f"Error in menu monitor: {e}")
                        await asyncio.sleep(5.0)

            # Start menu monitoring task
            menu_task = asyncio.create_task(_monitor_menu_events())

            # Wait for Claude Code to finish
            await asyncio.to_thread(proc.wait)
            
            # Cleanup
            menu_task.cancel()
            
            # Gracefully stop Telegram bot with error suppression
            try:
                await tg_mgr.stop()
            except Exception as e:
                # Suppress common Telegram shutdown errors to reduce log noise
                if "still running" in str(e) or "getUpdates" in str(e):
                    logger.debug(f"Expected Telegram shutdown error: {e}")
                else:
                    logger.error(f"Unexpected Telegram shutdown error: {e}")
            
            menu_coordinator.clear_state_files()
        
        finally:
            # Clean up lock file
            try:
                lock_file.unlink(missing_ok=True)
            except Exception:
                pass

    asyncio.run(_async_main())


@app.command("transparent", hidden=True)
def transparent(
    plan_mode: bool = typer.Option(False, "--plan", help="Start Claude Code in plan mode"),
    cli_args: List[str] = typer.Argument(None, help="Arguments forwarded to claude-code"),
) -> None:
    """Completely transparent mode - Claude Code works exactly as normal, with hook-based monitoring."""
    import json
    import os
    import asyncio
    
    async def _start_transparent():
        from remocode.src.config import config_manager
        
        # Send startup notification to Telegram (simple approach)
        env = config_manager.env
        if env.tg_token and env.tg_chat:
            try:
                import urllib.request
                import urllib.parse
                
                # Simple direct Telegram API call
                mode_text = "plan mode" if plan_mode else "normal mode"
                message = f"🚀 Claude Code started in {mode_text} (transparent)"
                
                url = f"https://api.telegram.org/bot{env.tg_token}/sendMessage"
                data = urllib.parse.urlencode({
                    'chat_id': env.tg_chat,
                    'text': message,
                    'parse_mode': 'Markdown'
                }).encode()
                
                req = urllib.request.Request(url, data=data)
                urllib.request.urlopen(req, timeout=5)
            except Exception:
                pass  # Don't fail if Telegram doesn't work
        
        # Set up hooks by merging with existing settings
        settings_dir = Path(".claude")
        settings_dir.mkdir(exist_ok=True)
        settings_file = settings_dir / "settings.local.json"
        
        hook_script = Path(__file__).parent / "hooks" / "pretool_guard.py"
        
        # Load existing config or create new
        config = {}
        if settings_file.exists():
            try:
                config = json.loads(settings_file.read_text())
            except json.JSONDecodeError:
                config = {}
        
        # Ensure hooks section exists
        hooks_section = config.setdefault("hooks", {})
        
        # Add PreToolUse hook for pretool_guard
        pre_tool_hooks = hooks_section.setdefault("PreToolUse", [])
        command_str = f"python3 {hook_script.resolve()}"
        
        # Check if our hook is already present
        found = False
        for matcher in pre_tool_hooks:
            for h in matcher.get("hooks", []):
                if h.get("command") == command_str:
                    found = True
                    break
        
        if not found:
            pre_tool_hooks.append({
                "hooks": [
                    {
                        "type": "command",
                        "command": command_str
                    }
                ]
            })
        
        # Add Notification hook for plan approval menus if not present
        notification_hooks = hooks_section.setdefault("Notification", [])
        notification_cmd = f"python3 {Path(__file__).parent / 'hooks' / 'notification_handler.py'}"
        
        notification_found = False
        for matcher in notification_hooks:
            for h in matcher.get("hooks", []):
                if "notification_handler.py" in h.get("command", ""):
                    notification_found = True
                    break
        
        if not notification_found:
            notification_hooks.append({
                "hooks": [
                    {
                        "type": "command",
                        "command": f"{notification_cmd} \"$CLAUDE_NOTIFICATION_TYPE\" \"$CLAUDE_NOTIFICATION_MESSAGE\""
                    }
                ]
            })
        
        # Add UserPromptSubmit hook for menu detection if not present
        prompt_hooks = hooks_section.setdefault("UserPromptSubmit", [])
        prompt_cmd = f"python3 {Path(__file__).parent / 'hooks' / 'menu_detector.py'}"
        
        prompt_found = False
        for matcher in prompt_hooks:
            for h in matcher.get("hooks", []):
                if "menu_detector.py" in h.get("command", ""):
                    prompt_found = True
                    break
        
        if not prompt_found:
            prompt_hooks.append({
                "hooks": [
                    {
                        "type": "command",
                        "command": f"{prompt_cmd} \"$CLAUDE_USER_PROMPT\""
                    }
                ]
            })
        
        settings_file.write_text(json.dumps(config, indent=2))
        
        # Build command
        args = cli_args or []
        
        # Add plan mode if requested
        if plan_mode:
            args = ["--permission-mode", "plan"] + args
        
        cmd = ["npx", "@anthropic-ai/claude-code"] + args
        
        # Execute Claude Code directly, replacing this process
        os.execvp(cmd[0], cmd)
    
    # Run the async startup
    asyncio.run(_start_transparent())


if __name__ == "__main__":
    app()