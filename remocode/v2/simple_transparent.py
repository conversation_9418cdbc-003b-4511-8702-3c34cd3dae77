#!/usr/bin/env python3
"""
Simple transparent mode for RemoCode v2
Just runs Claude Code with hooks enabled, no output interception
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    # Set up hooks by ensuring settings.local.json exists
    settings_dir = Path(".claude")
    settings_dir.mkdir(exist_ok=True)
    settings_file = settings_dir / "settings.local.json"
    
    hook_script = Path(__file__).parent / "hooks" / "pretool_guard.py"
    
    # Simple hook configuration
    config = {
        "hooks": {
            "PreToolUse": [
                {
                    "hooks": [
                        {
                            "type": "command",
                            "command": f"python3 {hook_script.resolve()}"
                        }
                    ]
                }
            ]
        }
    }
    
    import json
    settings_file.write_text(json.dumps(config, indent=2))
    
    # Build command
    args = sys.argv[1:] if len(sys.argv) > 1 else []
    
    # Add plan mode if requested
    if "--plan" in args:
        args.remove("--plan")
        args = ["--permission-mode", "plan"] + args
    
    cmd = ["npx", "@anthropic-ai/claude-code"] + args
    
    # Execute Claude Code directly, replacing this process
    os.execvp(cmd[0], cmd)

if __name__ == "__main__":
    main()