2025-07-21 21:38:32,945 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getMe "HTTP/1.1 200 OK"
2025-07-21 21:38:32,947 - INFO - Telegram bot connected: @gh_coding_agent_bot
2025-07-21 21:38:33,106 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getMe "HTTP/1.1 200 OK"
2025-07-21 21:38:33,108 - INFO - Application started
2025-07-21 21:38:33,483 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/sendMessage "HTTP/1.1 200 OK"
2025-07-21 21:38:33,489 - INFO - Telegram bot started successfully
2025-07-21 21:38:33,489 - INFO - Launching Claude CLI: npx @anthropic-ai/claude-code
2025-07-21 21:38:33,673 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/deleteWebhook "HTTP/1.1 200 OK"
2025-07-21 21:38:35,769 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/sendMessage "HTTP/1.1 200 OK"
2025-07-21 21:38:35,770 - INFO - Created new live view message 470
2025-07-21 21:38:36,349 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/sendMessage "HTTP/1.1 200 OK"
2025-07-21 21:38:36,350 - INFO - Created new live view message 471
2025-07-21 21:38:37,728 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:38:39,514 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:40,895 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:42,357 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:43,789 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:44,362 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 200 OK"
2025-07-21 21:38:45,144 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:46,517 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:47,882 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:49,258 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:50,619 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:52,023 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:53,511 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:54,703 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 200 OK"
2025-07-21 21:38:54,892 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:56,256 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:57,617 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:38:58,995 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 400 Bad Request"
2025-07-21 21:39:00,036 - INFO - Claude CLI exited with code 0
2025-07-21 21:39:00,037 - INFO - ClaudeStreamer stopped.
2025-07-21 21:39:00,038 - INFO - ClaudeStreamer stopped.
2025-07-21 21:39:00,039 - INFO - Stopping Telegram bot...
2025-07-21 21:39:00,040 - INFO - Application is stopping. This might take a moment.
2025-07-21 21:39:00,041 - INFO - Application.stop() complete
2025-07-21 21:39:00,043 - ERROR - Error stopping Telegram application: This Updater is still running!
2025-07-21 21:39:00,044 - INFO - Telegram bot stopped
2025-07-21 21:39:00,063 - ERROR - Task exception was never retrieved
future: <Task finished name='Task-36' coro=<Updater._start_polling.<locals>.polling_action_cb() done, defined at /Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py:338> exception=NetworkError('httpx.ReadError: ')>
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_backends/anyio.py", line 32, in read
    with map_exceptions(exc_map):
  File "/Users/<USER>/.pyenv/versions/3.10.1/lib/python3.10/contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "/Users/<USER>/.pyenv/versions/3.10.1/lib/python3.10/contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ReadError: 
2025-07-21 21:39:07,012 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getMe "HTTP/1.1 200 OK"
2025-07-21 21:39:07,013 - INFO - Telegram bot connected: @gh_coding_agent_bot
2025-07-21 21:39:07,185 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getMe "HTTP/1.1 200 OK"
2025-07-21 21:39:07,190 - INFO - Application started
2025-07-21 21:39:07,756 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/sendMessage "HTTP/1.1 200 OK"
2025-07-21 21:39:07,762 - INFO - Telegram bot started successfully
2025-07-21 21:39:07,764 - INFO - Launching Claude CLI: npx @anthropic-ai/claude-code run
2025-07-21 21:39:08,104 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/deleteWebhook "HTTP/1.1 200 OK"
2025-07-21 21:39:09,853 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/sendMessage "HTTP/1.1 200 OK"
2025-07-21 21:39:09,854 - INFO - Created new live view message 473
2025-07-21 21:39:10,248 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/sendMessage "HTTP/1.1 200 OK"
2025-07-21 21:39:10,249 - INFO - Created new live view message 474
2025-07-21 21:39:11,629 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:12,996 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:14,377 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:14,768 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 409 Conflict"
2025-07-21 21:39:14,772 - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 21:39:15,763 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:17,196 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:18,567 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:19,954 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:21,354 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:22,745 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:23,700 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 409 Conflict"
2025-07-21 21:39:23,703 - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-21 21:39:24,124 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:25,498 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:26,883 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:28,270 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:29,650 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:31,018 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:32,396 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:33,789 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:35,174 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:35,550 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 200 OK"
2025-07-21 21:39:36,550 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:37,961 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:39,392 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:40,802 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:42,202 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:42,497 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 200 OK"
2025-07-21 21:39:42,992 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/answerCallbackQuery "HTTP/1.1 200 OK"
2025-07-21 21:39:43,570 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:43,905 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 200 OK"
2025-07-21 21:39:44,403 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/answerCallbackQuery "HTTP/1.1 200 OK"
2025-07-21 21:39:45,002 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:46,398 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:47,769 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:49,183 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:50,570 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:51,942 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:53,328 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:54,223 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 200 OK"
2025-07-21 21:39:54,695 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:56,101 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:57,500 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:39:58,868 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:40:00,243 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:40:01,624 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:40:03,044 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:40:04,414 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/editMessageText "HTTP/1.1 200 OK"
2025-07-21 21:40:04,537 - INFO - HTTP Request: POST https://api.telegram.org/bot7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo/getUpdates "HTTP/1.1 200 OK"
2025-07-21 21:40:04,546 - INFO - Claude CLI exited with code 0
2025-07-21 21:40:04,548 - INFO - ClaudeStreamer stopped.
2025-07-21 21:40:04,549 - INFO - ClaudeStreamer stopped.
2025-07-21 21:40:04,550 - INFO - Stopping Telegram bot...
2025-07-21 21:40:04,552 - INFO - Application is stopping. This might take a moment.
2025-07-21 21:40:04,554 - INFO - Application.stop() complete
2025-07-21 21:40:04,556 - ERROR - Error stopping Telegram application: This Updater is still running!
2025-07-21 21:40:04,557 - INFO - Telegram bot stopped
2025-07-21 21:40:04,575 - ERROR - Task exception was never retrieved
future: <Task finished name='Task-739' coro=<Updater._start_polling.<locals>.polling_action_cb() done, defined at /Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py:338> exception=NetworkError('httpx.ReadError: ')>
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_backends/anyio.py", line 32, in read
    with map_exceptions(exc_map):
  File "/Users/<USER>/.pyenv/versions/3.10.1/lib/python3.10/contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "/Users/<USER>/.pyenv/versions/3.10.1/lib/python3.10/contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ReadError: 
2025-07-21 22:02:55,656 - ERROR - Error stopping Telegram application: This Updater is still running!
2025-07-21 22:02:55,670 - ERROR - Task exception was never retrieved
future: <Task finished name='Task-50' coro=<Updater._start_polling.<locals>.polling_action_cb() done, defined at /Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py:338> exception=NetworkError('httpx.ReadError: ')>
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_backends/anyio.py", line 32, in read
    with map_exceptions(exc_map):
  File "/Users/<USER>/.pyenv/versions/3.10.1/lib/python3.10/contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "/Users/<USER>/.pyenv/versions/3.10.1/lib/python3.10/contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ReadError: 
2025-07-21 22:03:57,743 - ERROR - Error stopping Telegram application: This Updater is still running!
2025-07-21 22:03:57,762 - ERROR - Task exception was never retrieved
future: <Task finished name='Task-43' coro=<Updater._start_polling.<locals>.polling_action_cb() done, defined at /Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py:338> exception=NetworkError('httpx.ReadError: ')>
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_backends/anyio.py", line 32, in read
    with map_exceptions(exc_map):
  File "/Users/<USER>/.pyenv/versions/3.10.1/lib/python3.10/contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "/Users/<USER>/.pyenv/versions/3.10.1/lib/python3.10/contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/Users/<USER>/Desktop/github/next13-clarify/.venv/lib/python3.10/site-packages/telegram/request/_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ReadError: 
2025-07-21 22:07:09,322 - INFO - Telegram not configured, running in terminal-only mode
