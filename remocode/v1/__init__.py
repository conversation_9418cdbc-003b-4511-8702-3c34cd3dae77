"""RemoCode v1 – alias pointing to original screen-scraping implementation in remocode.src.*"""

import importlib, sys as _sys, types as _types

_src_pkg = importlib.import_module("remocode.src")

# Re-export common attributes so `import remocode.v1.some_module` works
for _name, _mod in _sys.modules.items():
    if _name.startswith("remocode.src"):
        v1_name = _name.replace("remocode.src", "remocode.v1", 1)
        _sys.modules[v1_name] = _mod

def __getattr__(name: str):  # noqa: D401
    return getattr(_src_pkg, name)