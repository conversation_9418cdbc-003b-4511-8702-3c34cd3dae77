"""
Asyncio-based wrapper for Claude CLI subprocess.

Provides bidirectional communication between Claude CLI and both
terminal and Telegram interfaces with proper menu detection.
"""

import asyncio
import logging
import os
import pty
import signal
import sys
import re
from typing import Optional, Callable, AsyncIterator, List, Union
from pathlib import Path

from .menu_detector import <PERSON>uD<PERSON><PERSON>, MenuResponseHandler, DetectedMenu
from .state_manager import StateManager


logger = logging.getLogger(__name__)


class ClaudeWrapper:
    """Asyncio-based wrapper for Claude CLI subprocess."""

    def __init__(
        self,
        claude_cmd: str = "npx @anthropic-ai/claude-code",
        state_manager: Optional[StateManager] = None,
        on_output: Optional[Callable[[str], None]] = None,
        on_menu_detected: Optional[Callable[[DetectedMenu], None]] = None
    ):
        """
        Initialize Claude wrapper.

        Args:
            claude_cmd: Command to start Claude CLI
            state_manager: Optional state manager for persistence
            on_output: Optional callback for output lines
            on_menu_detected: Optional callback for detected menus
        """
        self.claude_cmd = claude_cmd
        self.state_manager = state_manager
        self.on_output = on_output
        self.on_menu_detected = on_menu_detected
        self.on_state_change: Optional[Callable[[str, str], None]] = None  # (old_state, new_state)

        # Subprocess management
        self.process: Optional[asyncio.subprocess.Process] = None
        self.stdin_writer: Optional[asyncio.StreamWriter] = None
        self.stdout_reader: Optional[asyncio.StreamReader] = None
        self.master_fd: Optional[int] = None
        self.running = False

        # Terminal state management
        self.original_terminal_attrs = None

        # Menu and response handling
        self.menu_detector = MenuDetector()
        self.response_handler = MenuResponseHandler()

        # Output streaming
        self.output_queue: asyncio.Queue = asyncio.Queue()

        # Tasks for concurrent operations
        self.tasks: List[asyncio.Task] = []

        # Debounce mechanism for menu detection
        self.is_menu_active = False

        # Keystroke batching for better UX
        self.keystroke_buffer = b""
        self.last_keystroke_time = 0
        self.keystroke_batch_interval = 0.5  # 500ms - much more responsive

        # State detection for adaptive streaming
        self.claude_state = "waiting"  # waiting, thinking, streaming, tool_use
        self.last_output_time = 0

        # Hook-based state monitoring
        self.hook_state_file = Path(".remocode_state.json")
        self.last_hook_check = 0
        self.hook_check_interval = 1.0  # Check hooks every second

        # Output line buffering for Telegram
        self.output_line_buffer: List[str] = []
        self.last_output_send_time = 0
        self.output_buffer_interval = 2.0  # Send output every 2 seconds

    async def start(self, args: List[str] = None) -> bool:
        """
        Start the Claude CLI subprocess.

        Args:
            args: Additional command line arguments

        Returns:
            True if started successfully, False otherwise
        """
        if self.running:
            logger.warning("Claude wrapper already running")
            return False

        try:
            # Build command
            cmd_parts = self.claude_cmd.split()
            if args:
                cmd_parts.extend(args)

            logger.info(f"Starting Claude CLI: {' '.join(cmd_parts)}")

            # Use PTY to make Claude CLI think it's running in a real terminal
            logger.debug(f"Creating PTY subprocess with command: {cmd_parts}")

            cmd_str = " ".join(cmd_parts)
            logger.debug(f"Using shell command with PTY: {cmd_str}")

            # Create PTY master and slave
            master_fd, slave_fd = pty.openpty()

            # Set environment variables for better terminal experience
            env = os.environ.copy()
            env['TERM'] = 'xterm-256color'
            env['FORCE_COLOR'] = '1'
            env['PYTHONUNBUFFERED'] = '1'

            # Start subprocess with PTY
            self.process = await asyncio.create_subprocess_shell(
                cmd_str,
                stdin=slave_fd,
                stdout=slave_fd,
                stderr=slave_fd,
                env=env,
                preexec_fn=os.setsid  # Create new session
            )

            # Close slave fd in parent process
            os.close(slave_fd)

            # Create streams from master fd
            loop = asyncio.get_event_loop()
            self.master_fd = master_fd

            # Create a StreamReader for reading from PTY master
            self.stdout_reader = asyncio.StreamReader()
            reader_protocol = asyncio.StreamReaderProtocol(self.stdout_reader)
            reader_transport, _ = await loop.connect_read_pipe(
                lambda: reader_protocol,
                os.fdopen(master_fd, 'rb', buffering=0)
            )

            # Create a StreamWriter for writing to PTY master
            # Use a simple protocol that just stores the transport
            class WriteProtocol(asyncio.Protocol):
                def __init__(self):
                    self.transport = None

                def connection_made(self, transport):
                    self.transport = transport

                def connection_lost(self, exc):
                    self.transport = None

            writer_protocol = WriteProtocol()
            writer_transport, _ = await loop.connect_write_pipe(
                lambda: writer_protocol,
                os.fdopen(os.dup(master_fd), 'wb', buffering=0)
            )

            # Create StreamWriter with proper parameters
            self.stdin_writer = asyncio.StreamWriter(
                transport=writer_transport,
                protocol=writer_protocol,
                reader=None,
                loop=loop
            )
            logger.debug(f"PTY subprocess created successfully, PID: {self.process.pid}")
            logger.debug(f"PTY master fd: {self.master_fd}")
            logger.debug(f"PTY stdin writer: {self.stdin_writer}")
            logger.debug(f"PTY stdout reader: {self.stdout_reader}")

            # Save PID to state manager
            if self.state_manager:
                self.state_manager.set_claude_pid(self.process.pid)

            self.running = True
            logger.info(f"Claude CLI started with PID {self.process.pid}")

            # Start concurrent tasks - need output reader and input forwarder for PTY
            self.tasks = [
                asyncio.create_task(self._read_pty_output()),
                asyncio.create_task(self._forward_terminal_input()),
                asyncio.create_task(self._process_output_queue()),
                asyncio.create_task(self._process_keystroke_batches()),
                asyncio.create_task(self._monitor_hook_state()),
                asyncio.create_task(self._process_output_buffer())
            ]

            # Give Claude CLI a moment to initialize
            await asyncio.sleep(0.5)

            # Note: Removed automatic initial prompt to avoid unexpected token usage
            # Claude CLI will show its own welcome prompt

            return True

        except Exception as e:
            logger.error(f"Failed to start Claude CLI: {e}")
            await self.stop()
            return False

    async def stop(self) -> None:
        """Stop the Claude CLI subprocess and cleanup."""
        if not self.running:
            return

        logger.info("Stopping Claude wrapper...")
        self.running = False

        # Cancel all tasks
        for task in self.tasks:
            if not task.done():
                task.cancel()

        # Wait for tasks to complete with timeout
        if self.tasks:
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*self.tasks, return_exceptions=True),
                    timeout=5.0
                )
                # Log any exceptions from background tasks
                for i, result in enumerate(results):
                    if isinstance(result, Exception) and not isinstance(result, asyncio.CancelledError):
                        logger.error(f"Background task {i} failed with: {result}")
            except asyncio.TimeoutError:
                logger.warning("Some tasks didn't complete within timeout")

        # Close stdin writer
        if self.stdin_writer and not self.stdin_writer.is_closing():
            self.stdin_writer.close()
            try:
                await self.stdin_writer.wait_closed()
            except Exception:
                pass

        # Close PTY master fd if we have one
        if hasattr(self, 'master_fd') and self.master_fd:
            try:
                os.close(self.master_fd)
                logger.debug("Closed PTY master fd")
            except Exception as e:
                logger.debug(f"Error closing PTY master fd: {e}")
            self.master_fd = None

        # Terminate process
        if self.process:
            try:
                self.process.terminate()
                await asyncio.wait_for(self.process.wait(), timeout=3.0)
            except asyncio.TimeoutError:
                logger.warning("Claude process didn't terminate gracefully, killing...")
                self.process.kill()
                try:
                    await self.process.wait()
                except Exception:
                    pass
            except Exception as e:
                logger.error(f"Error terminating Claude process: {e}")

        self.process = None
        self.stdin_writer = None
        self.tasks.clear()

        # Restore terminal attributes if needed
        self._cleanup_terminal()

        logger.info("Claude wrapper stopped")

    def _cleanup_terminal(self) -> None:
        """Clean up terminal state."""
        if self.original_terminal_attrs:
            try:
                import termios
                stdin_fd = sys.stdin.fileno()
                termios.tcsetattr(stdin_fd, termios.TCSADRAIN, self.original_terminal_attrs)
                logger.debug("Cleaned up terminal attributes in stop()")
                self.original_terminal_attrs = None
            except Exception as e:
                logger.debug(f"Error cleaning up terminal attributes: {e}")

    async def send_input(self, text: str, source: str = "unknown") -> bool:
        """
        Send input to Claude CLI.

        Args:
            text: Input text to send
            source: Source of the input ("terminal", "telegram", etc.)

        Returns:
            True if sent successfully, False otherwise
        """
        if not self.running:
            logger.error("Cannot send input: Claude wrapper not running")
            return False

        try:
            # Ensure the input ends with carriage return so it gets executed.
            # If the caller already appended a newline we convert it to \r.
            if text.endswith('\n') and not text.endswith('\r'):
                text = text.rstrip('\n') + '\r'
            elif not text.endswith('\r'):
                text += '\r'

            logger.info(f"Sending input from {source}: {text.strip()}")
            logger.debug(f"Writing {len(text)} bytes to stdin: {text.encode('utf-8')}")

            # Write directly to the PTY master fd if we have it
            if hasattr(self, 'master_fd') and self.master_fd:
                data = text.encode('utf-8')
                bytes_written = os.write(self.master_fd, data)
                logger.debug(f"Wrote {bytes_written} bytes to PTY master fd")
            elif self.stdin_writer:
                self.stdin_writer.write(text.encode('utf-8'))
                await self.stdin_writer.drain()
                logger.debug("Input sent via StreamWriter and drained successfully")
            else:
                logger.error("No available method to send input to Claude")
                return False

            # Update state manager
            if self.state_manager:
                self.state_manager.add_log_line(f"INPUT[{source}]: {text.strip()}")

            return True

        except Exception as e:
            logger.error(f"Failed to send input to Claude: {e}")
            return False

    async def wait_for_menu_choice(self, timeout: float = 300.0) -> Optional[str]:
        """
        Wait for user choice on a detected menu.

        Args:
            timeout: Maximum time to wait in seconds

        Returns:
            User's choice or None if timeout
        """
        return await self.response_handler.wait_for_choice(timeout)

    def submit_menu_choice(self, choice: str, source: str = "unknown") -> bool:
        """
        Submit a menu choice from terminal or Telegram.

        Args:
            choice: User's choice
            source: Source of the choice

        Returns:
            True if choice was accepted, False otherwise
        """
        return self.response_handler.submit_choice(choice, source)

    async def _read_output(self) -> None:
        """Read output from Claude CLI subprocess."""
        if not self.process or not self.process.stdout:
            logger.error("No process or stdout available for reading")
            return

        logger.debug("Started reading Claude output")
        try:
            async for line in self._readline_iterator(self.process.stdout):
                if not self.running:
                    break

                logger.debug(f"Read line from Claude: {line[:50]}...")
                await self.output_queue.put(('output', line))

        except Exception as e:
            if self.running:  # Only log if we're not shutting down
                logger.error(f"Error reading Claude output: {e}")
        finally:
            logger.debug("Claude output reading ended")
            if self.running:
                await self.output_queue.put(('eof', None))

    async def _read_stderr(self) -> None:
        """Read stderr from Claude CLI subprocess for debugging."""
        if not self.process or not self.process.stderr:
            logger.debug("No process stderr available for reading")
            return

        logger.debug("Started reading Claude stderr")
        try:
            async for line in self._readline_iterator(self.process.stderr):
                if not self.running:
                    break

                logger.warning(f"Claude CLI stderr: {line}")
                # Also put stderr into output queue for visibility
                await self.output_queue.put(('stderr', line))

        except Exception as e:
            if self.running:
                logger.error(f"Error reading Claude stderr: {e}")
        finally:
            logger.debug("Claude stderr reading ended")

    async def _read_pty_output(self) -> None:
        """Read output from Claude CLI via PTY."""
        if not self.stdout_reader:
            logger.error("No PTY stdout reader available")
            return

        logger.debug("Started reading Claude PTY output")
        buffer = ""
        try:
            # Use read(n) instead of readline() to handle raw TUI streams
            # that may not use newlines frequently, which would block readline().
            while self.running:
                data_bytes = await self.stdout_reader.read(1024)
                if not data_bytes:  # EOF
                    logger.debug("PTY stream ended (EOF)")
                    break

                # Append new data to buffer and decode
                data = buffer + data_bytes.decode('utf-8', errors='replace')
                lines = data.splitlines(True)  # keepends=True

                # If the last element does not end with a newline, it's a partial line.
                # Keep it in the buffer for the next read.
                if lines and not lines[-1].endswith(('\n', '\r')):
                    buffer = lines.pop()
                else:
                    buffer = ""

                for line in lines:
                    if not self.running:
                        break

                    logger.debug(f"Read PTY line from chunk: {line[:50].replace(chr(27), '[ESC]')}")
                    await self.output_queue.put(('output', line))

        except Exception as e:
            if self.running:
                logger.error(f"Error reading Claude PTY output: {e}", exc_info=True)
        finally:
            logger.debug("Claude PTY output reading ended")
            if self.running:
                await self.output_queue.put(('eof', None))

    async def _readline_iterator(self, stream: asyncio.StreamReader) -> AsyncIterator[str]:
        """Async iterator for reading lines from stream."""
        logger.debug("Starting readline iterator")
        line_count = 0
        while True:
            try:
                logger.debug(f"Waiting for line {line_count + 1}...")
                line_bytes = await stream.readline()
                if not line_bytes:  # EOF
                    logger.debug("Reached EOF in readline iterator")
                    break

                # Preserve all control characters (\r, \n, ANSI, etc.) so the TUI renders correctly
                line = line_bytes.decode('utf-8', errors='replace')
                line_count += 1
                logger.debug(f"Read line {line_count}: {line[:100]}...")
                yield line

            except Exception as e:
                logger.error(f"Error reading line from stream: {e}")
                break
        logger.debug(f"Readline iterator finished after {line_count} lines")

    async def _handle_terminal_input(self) -> None:
        """
        DEPRECATED: Handle input from terminal stdin using proper asyncio.

        This method is no longer used to allow Claude CLI to handle input naturally.
        Keeping for potential future menu-only input handling.
        """
        try:
            import platform
            if platform.system() == 'Windows':
                # Use aioconsole for Windows compatibility
                try:
                    import aioconsole
                    while self.running:
                        try:
                            line = await aioconsole.ainput()
                            if line and self.running:
                                # Submit directly to response handler
                                self.response_handler.submit_choice(line.strip(), "terminal")
                        except EOFError:
                            break
                        except Exception as e:
                            if self.running:
                                logger.error(f"Error reading terminal input: {e}")
                            break
                except ImportError:
                    logger.warning("aioconsole not available on Windows, terminal input disabled")
                    return
            else:
                # Unix/Linux: use event loop reader - but check if stdin is available
                try:
                    # Check if stdin is available and not redirected
                    if not sys.stdin.isatty():
                        logger.debug("Stdin is not a TTY, skipping terminal input handler")
                        return

                    loop = asyncio.get_event_loop()

                    def stdin_callback():
                        """Callback for when stdin has data available."""
                        try:
                            line = sys.stdin.readline()
                            if line and self.running:
                                # Submit directly to response handler for menu responses
                                self.response_handler.submit_choice(line.strip(), "terminal")
                        except Exception as e:
                            logger.error(f"Error in stdin callback: {e}")

                    # Set stdin to non-blocking mode and add reader
                    import os
                    import fcntl
                    fd = sys.stdin.fileno()
                    flags = fcntl.fcntl(fd, fcntl.F_GETFL)
                    fcntl.fcntl(fd, fcntl.F_SETFL, flags | os.O_NONBLOCK)

                    loop.add_reader(sys.stdin.fileno(), stdin_callback)
                    logger.debug("Added stdin reader for terminal input")

                    # Keep the task alive
                    while self.running:
                        await asyncio.sleep(1)

                    # Cleanup
                    try:
                        loop.remove_reader(sys.stdin.fileno())
                        # Restore blocking mode
                        fcntl.fcntl(fd, fcntl.F_SETFL, flags)
                        logger.debug("Cleaned up stdin reader")
                    except:
                        pass

                except Exception as e:
                    logger.debug(f"Could not set up stdin reader: {e}, terminal input disabled")
                    # Just wait without terminal input capability
                    while self.running:
                        await asyncio.sleep(1)

        except Exception as e:
            if self.running:
                logger.error(f"Terminal input handler error: {e}")
            # Don't let terminal input errors crash the whole wrapper
            while self.running:
                await asyncio.sleep(1)

    async def _forward_terminal_input(self) -> None:
        """Forward terminal input to Claude CLI PTY using raw terminal mode."""
        import platform

        if platform.system() == 'Windows':
            await self._forward_terminal_input_windows()
        else:
            await self._forward_terminal_input_unix()

    async def _forward_terminal_input_windows(self) -> None:
        """Windows terminal input forwarding using msvcrt."""
        try:
            import msvcrt
            logger.debug("Starting Windows terminal input forwarding")

            while self.running:
                try:
                    # Check if key is available
                    if msvcrt.kbhit():
                        # Read the key without echo
                        key = msvcrt.getch()
                        if key and self.running and hasattr(self, 'master_fd') and self.master_fd:
                            # Forward raw key to PTY master fd
                            os.write(self.master_fd, key)
                    else:
                        # Small sleep to prevent busy waiting
                        await asyncio.sleep(0.01)

                except Exception as e:
                    if self.running:
                        logger.debug(f"Windows input forwarding error: {e}")
                    break

        except ImportError:
            logger.warning("msvcrt not available, using aioconsole fallback")
            await self._forward_terminal_input_windows_fallback()
        except Exception as e:
            logger.error(f"Windows terminal input forwarding failed: {e}")

    async def _forward_terminal_input_windows_fallback(self) -> None:
        """Windows fallback using aioconsole."""
        try:
            import aioconsole
            while self.running:
                try:
                    line = await aioconsole.ainput()
                    if line and self.running and hasattr(self, 'master_fd') and self.master_fd:
                        os.write(self.master_fd, (line + '\n').encode('utf-8'))
                except EOFError:
                    break
                except Exception as e:
                    if self.running:
                        logger.debug(f"Windows fallback input error: {e}")
                    break
        except ImportError:
            logger.warning("aioconsole not available, Windows input forwarding disabled")

    async def _forward_terminal_input_unix(self) -> None:
        """Unix terminal input forwarding using raw mode."""
        # Check if we have the necessary components
        if not (hasattr(self, 'master_fd') and self.master_fd):
            logger.debug("No PTY master fd available for input forwarding")
            return

        try:
            import termios
            import tty
            import select

            # Get terminal file descriptor
            stdin_fd = sys.stdin.fileno()

            # Save original terminal attributes
            self.original_terminal_attrs = termios.tcgetattr(stdin_fd)
            logger.debug("Saved original terminal attributes")

            try:
                # Set terminal to raw mode to capture all keystrokes
                tty.setraw(stdin_fd)
                logger.debug("Set terminal to raw mode for input forwarding")

                while self.running:
                    # Use select to check if input is available (non-blocking)
                    ready, _, _ = select.select([stdin_fd], [], [], 0.01)

                    if ready:
                        # Read raw input data
                        try:
                            data = os.read(stdin_fd, 1024)
                            if data and self.running:
                                import time
                                current_time = time.time()

                                # For immediate actions (Enter, Ctrl+C, etc.), forward immediately
                                if b'\n' in data or b'\r' in data or b'\x03' in data or b'\x04' in data:
                                    # Flush any buffered keystrokes first
                                    if self.keystroke_buffer:
                                        if self.master_fd:
                                            os.write(self.master_fd, self.keystroke_buffer)
                                            logger.debug(f"Flushed {len(self.keystroke_buffer)} buffered bytes before immediate action")
                                        self.keystroke_buffer = b""

                                    # Forward the immediate action
                                    if self.master_fd:
                                        bytes_written = os.write(self.master_fd, data)
                                        logger.debug(f"Immediately forwarded {len(data)} bytes (action key): {data}")

                                    # Check for Ctrl+C to gracefully exit
                                    if b'\x03' in data:  # Ctrl+C
                                        logger.debug("Ctrl+C detected in input, stopping")
                                        self.running = False
                                        break
                                elif len(data) == 1:
                                    # Single character - forward immediately for responsiveness
                                    if self.master_fd:
                                        bytes_written = os.write(self.master_fd, data)
                                        logger.debug(f"Immediately forwarded single char: {data}")
                                else:
                                    # Multiple characters (paste, etc.) - buffer briefly
                                    self.keystroke_buffer += data
                                    self.last_keystroke_time = current_time
                                    logger.debug(f"Buffered multi-char input {len(data)} bytes, buffer now {len(self.keystroke_buffer)} bytes")
                        except OSError as e:
                            if self.running:
                                logger.debug(f"Input read error (expected during shutdown): {e}")
                            break
                    else:
                        # No input available, small sleep to prevent busy waiting
                        await asyncio.sleep(0.01)

            finally:
                # Always restore original terminal attributes
                try:
                    if self.original_terminal_attrs:
                        termios.tcsetattr(stdin_fd, termios.TCSADRAIN, self.original_terminal_attrs)
                        logger.debug("Restored original terminal attributes")
                        self.original_terminal_attrs = None
                except:
                    logger.warning("Could not restore terminal attributes")

        except ImportError:
            logger.warning("termios/tty not available, input forwarding disabled")
        except Exception as e:
            logger.error(f"Unix terminal input forwarding error: {e}")
            # Try to restore terminal if possible
            try:
                import termios
                stdin_fd = sys.stdin.fileno()
                if self.original_terminal_attrs:
                    termios.tcsetattr(stdin_fd, termios.TCSADRAIN, self.original_terminal_attrs)
                    self.original_terminal_attrs = None
            except:
                pass

    async def _process_output_queue(self) -> None:
        """Process output queue and handle menu detection."""
        logger.debug("Started output queue processing")
        while self.running:
            try:
                item = await asyncio.wait_for(self.output_queue.get(), timeout=1.0)
                msg_type, content = item

                if msg_type == 'eof':
                    logger.info("Claude CLI process ended")
                    break
                elif msg_type == 'output':
                    await self._handle_output_line(content)
                elif msg_type == 'stderr':
                    await self._handle_stderr_line(content)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                if self.running:
                    logger.error(f"Error processing output queue: {e}")
        logger.debug("Output queue processing ended")

    async def _process_keystroke_batches(self) -> None:
        """Process keystroke batches to improve UX by reducing per-character messages."""
        logger.debug("Started keystroke batch processing")
        while self.running:
            try:
                await asyncio.sleep(0.5)  # Check every 500ms

                if self.keystroke_buffer and self.last_keystroke_time > 0:
                    import time
                    current_time = time.time()

                    # If enough time has passed since last keystroke, flush the buffer
                    if current_time - self.last_keystroke_time >= self.keystroke_batch_interval:
                        if self.master_fd:
                            os.write(self.master_fd, self.keystroke_buffer)
                            logger.debug(f"Batch flushed {len(self.keystroke_buffer)} bytes after timeout")
                            self.keystroke_buffer = b""
                            self.last_keystroke_time = 0

            except Exception as e:
                if self.running:
                    logger.error(f"Error processing keystroke batches: {e}")
        logger.debug("Keystroke batch processing ended")

    def _detect_claude_state(self, line: str) -> None:
        """Detect Claude's current state based on output patterns.

        The previous implementation was far too aggressive – every normal line that
        happened to contain the word *thinking* or *processing* triggered a state
        change which resulted in noisy "state change" messages and excessive live
        view splitting in Telegram.  This version keeps the logic simple and
        conservative:

        1. `tool_use` – match only obvious tool invocation markers.
        2. `thinking` – match the dedicated thinking indicator emitted by the
           Claude wrapper (lines that begin with the 🤔 emoji or ✻ bullet).
        3. `waiting` – when we clearly see a plan/interactive prompt.
        4. `streaming` – fallback for long continuous prose.

        Additionally we fix the bug where `self.last_output_time` was overwritten
        **before** we computed the idle duration, making the idle-based "waiting"
        branch unreachable.  We now update the timestamp only at the end of the
        routine.
        """

        import time

        current_time = time.time()
        time_since_last_output = current_time - self.last_output_time

        raw_line = line.rstrip("\n\r")
        clean_lower = raw_line.lower().strip()

        previous_state = self.claude_state

        # ------------------------------------------------------------------
        # 1. Tool usage detection (conservative)
        # ------------------------------------------------------------------
        if any(tok in clean_lower for tok in [
            "antml:function_calls", "<invoke", "bash", "read", "edit", "glob", "grep"
        ]):
            self.claude_state = "tool_use"

        # ------------------------------------------------------------------
        # 2. Thinking detection – look for dedicated markers (🤔 or ✻)
        # ------------------------------------------------------------------
        elif re.match(r"^[\s\[\(]*[🤔✻].*thinking", clean_lower):
            self.claude_state = "thinking"

        # ------------------------------------------------------------------
        # 3. Explicit interactive prompt / plan presentation → waiting
        # ------------------------------------------------------------------
        elif any(kw in clean_lower for kw in [
            "here is claude's plan", "ready to code", "would you like to proceed",
            "❯ 1.", "choose an option", "what would you like to do"
        ]):
            self.claude_state = "waiting"

        # ------------------------------------------------------------------
        # 4. Streaming: detect Anthropic streaming style or token counters
        # ------------------------------------------------------------------
        elif re.match(r"^\s*\d+\/\d+ tokens", clean_lower):
            # Example: "345/8192 tokens (calc)" from Claude CLI
            self.claude_state = "streaming"

        # ------------------------------------------------------------------
        # 5. No output for a while → revert to waiting
        # ------------------------------------------------------------------
        elif time_since_last_output > 5.0:
            self.claude_state = "waiting"

        # ------------------------------------------------------------------
        # Log & propagate state change
        # ------------------------------------------------------------------
        if previous_state != self.claude_state:
            logger.info(f"Claude state changed: {previous_state} -> {self.claude_state}")
            # Notify Telegram so it can decide whether to open a new live-view msg
            if self.on_state_change:
                try:
                    self.on_state_change(previous_state, self.claude_state)
                except Exception as exc:
                    logger.debug(f"on_state_change callback error: {exc}")

        # Finally record the time of this output line *after* processing so that
        # the idle calculation above is meaningful on subsequent calls.
        self.last_output_time = current_time

    def get_adaptive_batch_interval(self) -> float:
        """Get adaptive batch interval based on Claude's current state."""
        if self.claude_state == "thinking":
            return 8.0  # Slow updates during thinking
        elif self.claude_state == "streaming":
            return 5.0  # Medium updates during streaming responses
        elif self.claude_state == "tool_use":
            return 3.0  # Faster updates during tool usage
        else:  # waiting / input
            return 0.3  # Very responsive when user is typing

    async def _handle_output_line(self, line: str) -> None:
        """Handle a single output line from Claude."""
        # Debug logging
        logger.debug(f"Processing output line: {line[:100]}...")

        # Update Claude state detection
        self._detect_claude_state(line)

        # Print to terminal preserving ANSI control sequences and without forcing extra newlines
        if line:
            # Only add a newline if the original data didn't already end with one
            needs_newline = not (line.endswith("\n") or line.endswith("\r"))
            sys.stdout.write(line)
            if needs_newline:
                sys.stdout.write("\n")
            sys.stdout.flush()

        # Add to state manager log
        if self.state_manager:
            self.state_manager.add_log_line(f"OUTPUT: {line}")

        # Buffer output lines for less frequent sending to Telegram
        if line.strip():  # Only buffer non-empty lines
            self.output_line_buffer.append(line)
            logger.debug(f"Buffered output line, buffer size: {len(self.output_line_buffer)}")

        # For menu-related output, send immediately to show buttons quickly
        if "❯" in line or "Choose an option" in line or "Would you like to proceed" in line:
            await self._flush_output_buffer_now()

        # Check for menu detection in parallel.
        detected_menu = self.menu_detector.add_output_line(line)
        if detected_menu:
            # If a menu is already active, ignore this new detection.
            if self.is_menu_active:
                logger.debug(f"Ignoring new menu detection; a menu is already active.")
                return

            self.is_menu_active = True
            logger.info(f"Menu detected: {detected_menu.menu_type.value} with {detected_menu.option_count} options")

            # Update state manager
            if self.state_manager:
                menu_data = {
                    'type': detected_menu.menu_type.value,
                    'options': [
                        {'number': opt.number, 'text': opt.text}
                        for opt in detected_menu.options
                    ],
                    'timestamp': asyncio.get_event_loop().time()
                }
                self.state_manager.set_current_menu(menu_data)

            # Call menu detection callback
            if self.on_menu_detected:
                try:
                    self.on_menu_detected(detected_menu)
                except Exception as e:
                    logger.error(f"Error in menu detection callback: {e}")
                finally:
                    self.is_menu_active = False
                    logger.debug("Menu lock released.")

    async def _handle_stderr_line(self, line: str) -> None:
        """Handle a single stderr line from Claude."""
        # Print stderr to terminal with prefix
        print(f"[STDERR] {line}", flush=True)

        # Add to state manager log
        if self.state_manager:
            self.state_manager.add_log_line(f"STDERR: {line}")

        # Call output callback for stderr too
        if self.on_output:
            try:
                self.on_output(f"[STDERR] {line}")
            except Exception as e:
                logger.error(f"Error in stderr output callback: {e}")

    async def _monitor_hook_state(self) -> None:
        """Monitor Claude Code hooks for native state events."""
        logger.debug("Started hook state monitoring")
        while self.running:
            try:
                await asyncio.sleep(self.hook_check_interval)

                if self.hook_state_file.exists():
                    try:
                        # Read and parse hook state
                        import json
                        import time

                        with open(self.hook_state_file, 'r') as f:
                            hook_data = json.load(f)

                        event = hook_data.get("event")
                        timestamp = hook_data.get("timestamp")

                        if event and timestamp:
                            # Parse timestamp
                            import datetime
                            hook_time = datetime.datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            current_time = datetime.datetime.now(datetime.timezone.utc)

                            # Only process recent events (within last 30 seconds)
                            if (current_time - hook_time).total_seconds() < 30:
                                previous_state = self.claude_state

                                # Update state based on hook event
                                if event == "Start":
                                    self.claude_state = "thinking"
                                    logger.info("Claude started - state: thinking")
                                elif event == "ToolUse":
                                    self.claude_state = "tool_use"
                                    logger.info("Claude using tools - state: tool_use")
                                elif event == "Stop":
                                    self.claude_state = "waiting"
                                    logger.info("Claude finished - state: waiting")

                                # Log state changes from hooks
                                if previous_state != self.claude_state:
                                    logger.info(f"Hook-based state change: {previous_state} -> {self.claude_state}")
                                    # Notify about state change for new message creation
                                    if self.on_state_change:
                                        try:
                                            self.on_state_change(previous_state, self.claude_state)
                                        except Exception as e:
                                            logger.error(f"Error in state change callback: {e}")

                    except (json.JSONDecodeError, ValueError, KeyError) as e:
                        logger.debug(f"Error parsing hook state file: {e}")
                    except Exception as e:
                        logger.debug(f"Error reading hook state file: {e}")

            except Exception as e:
                if self.running:
                    logger.error(f"Error monitoring hook state: {e}")

        logger.debug("Hook state monitoring ended")

    async def _process_output_buffer(self) -> None:
        """Process buffered output lines and send to Telegram in batches."""
        logger.debug("Started output buffer processing")
        while self.running:
            try:
                # Use adaptive interval based on Claude state
                current_interval = min(self.output_buffer_interval, self.get_adaptive_batch_interval())
                await asyncio.sleep(current_interval)

                if self.output_line_buffer:
                    import time
                    current_time = time.time()

                    # Check if we should send the buffer
                    should_send = (
                        # Time-based: enough time has passed
                        current_time - self.last_output_send_time >= current_interval
                        # Size-based: buffer is getting large
                        or len(self.output_line_buffer) >= 20
                        # State-based: Claude is waiting (send immediately)
                        or self.claude_state == "waiting"
                    )

                    if should_send and self.on_output:
                        # Combine buffered lines into a single message
                        combined_output = '\n'.join(self.output_line_buffer)
                        logger.info(f"Sending buffered output: {len(self.output_line_buffer)} lines")

                        try:
                            maybe_coro = self.on_output(combined_output)
                            if asyncio.iscoroutine(maybe_coro):
                                await maybe_coro
                        except Exception as e:
                            logger.error(f"Error in on_output callback: {e}")

                        # Clear the buffer
                        self.output_line_buffer.clear()
                        self.last_output_send_time = current_time

            except Exception as e:
                if self.running:
                    logger.error(f"Error processing output buffer: {e}")

        logger.debug("Output buffer processing ended")

    async def _flush_output_buffer_now(self) -> None:
        """Immediately flush the output buffer to Telegram."""
        if self.output_line_buffer and self.on_output:
            import time
            combined_output = '\n'.join(self.output_line_buffer)
            logger.info(f"Immediately flushing output buffer: {len(self.output_line_buffer)} lines")

            try:
                maybe_coro = self.on_output(combined_output)
                if asyncio.iscoroutine(maybe_coro):
                    await maybe_coro
            except Exception as e:
                logger.error(f"Error in immediate flush callback: {e}")

            # Clear the buffer
            self.output_line_buffer.clear()
            self.last_output_send_time = time.time()

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop()

    @property
    def is_running(self) -> bool:
        """Check if Claude wrapper is running."""
        return self.running and self.process is not None