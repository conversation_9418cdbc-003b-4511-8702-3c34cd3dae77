#!/usr/bin/env python3
"""
Test script to verify only the Telegram bot functionality.
"""

import sys
import asyncio
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent))

from src.telegram_bot import TelegramBot, TELEGRAM_AVAILABLE
from src.config import EnvironmentConfig

async def test_telegram_bot():
    """Test Telegram bot basic functionality."""
    print("🧪 Testing Telegram Bot Functionality")
    print("=" * 40)
    
    # Check if Telegram is available
    if not TELEGRAM_AVAILABLE:
        print("❌ python-telegram-bot not available")
        return False
    
    # Load environment config
    env_config = EnvironmentConfig()
    
    # Check environment variables
    if not env_config.has_telegram:
        print("❌ Telegram not configured")
        print(f"TG_TOKEN present: {bool(os.getenv('TG_TOKEN'))}")
        print(f"TG_CHAT present: {bool(os.getenv('TG_CHAT'))}")
        
        # Try to show what vars are available
        telegram_vars = [k for k in os.environ.keys() if 'TG' in k.upper() or 'TELEGRAM' in k.upper()]
        if telegram_vars:
            print(f"Found Telegram-related vars: {telegram_vars}")
        return False
    
    print(f"✅ Telegram token: {env_config.tg_token[:10]}...")
    print(f"✅ Chat ID: {env_config.tg_chat}")
    
    # Create and test bot
    bot = TelegramBot(
        token=env_config.tg_token,
        chat_id=env_config.tg_chat,
        on_menu_choice=lambda choice, source: print(f"📋 Menu choice: {choice} from {source}"),
        on_command=lambda cmd, args: print(f"⚡ Command: /{cmd} {args}")
    )
    
    try:
        print("🚀 Starting Telegram bot...")
        success = await bot.start()
        if not success:
            print("❌ Failed to start Telegram bot")
            return False
        
        print("✅ Telegram bot started successfully")
        
        # Send test messages
        test_messages = [
            "🔧 RemoCode Telegram Test",
            "This is line 1 of Claude output",
            "This is line 2 of Claude output", 
            "This is line 3 with emojis 🚀 ✨ 🎉",
            "```python\\nprint('Code block test')\\n```"
        ]
        
        print("📤 Sending test messages...")
        for i, msg in enumerate(test_messages, 1):
            print(f"  Sending message {i}: {msg[:30]}...")
            await bot.send_output(msg, silent=False)
            await asyncio.sleep(0.5)
        
        print("⏳ Waiting for message batching (5 seconds)...")
        await asyncio.sleep(5)
        
        print("✅ Test messages sent successfully")
        print("💡 Check your Telegram chat to see the messages")
        
        return True
        
    except Exception as e:
        print(f"❌ Telegram bot test failed: {e}")
        return False
    finally:
        print("🛑 Stopping Telegram bot...")
        await bot.stop()

async def main():
    success = await test_telegram_bot()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)