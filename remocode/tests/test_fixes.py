#!/usr/bin/env python3
"""
Test script to verify the critical fixes work properly.
Tests the STOP-SHIP issues and other audit fixes.
"""

import sys
sys.path.insert(0, '.')

def test_imports():
    """Test that all modules import without errors."""
    print("🧪 Testing imports...")
    
    try:
        from src.claude_wrapper import ClaudeWrapper
        print("✅ ClaudeWrapper imports")
    except Exception as e:
        print(f"❌ ClaudeWrapper import failed: {e}")
        return False
    
    try:
        from src.telegram_bot import TelegramBot, TELEGRAM_AVAILABLE
        print(f"✅ TelegramBot imports (telegram available: {TELEGRAM_AVAILABLE})")
    except Exception as e:
        print(f"❌ TelegramBot import failed: {e}")
        return False
    
    try:
        from src.state_manager import StateManager
        print("✅ StateManager imports")
    except Exception as e:
        print(f"❌ StateManager import failed: {e}")
        return False
    
    return True

def test_rate_limiter():
    """Test the rate limiter fix for negative sleep."""
    print("\n🧪 Testing rate limiter fixes...")
    
    from src.telegram_bot import TelegramBot
    import time
    
    # Create a dummy bot instance to test the rate limiter
    bot = object.__new__(TelegramBot)
    bot.message_count_window = []
    
    # Fill with recent messages to trigger rate limiting
    current_time = time.time()
    bot.message_count_window = [current_time - 0.1] * 30  # 30 recent messages
    
    should_limit, wait_time = bot._should_rate_limit()
    
    if should_limit and wait_time >= 0:
        print(f"✅ Rate limiter works correctly: should_limit={should_limit}, wait_time={wait_time:.3f}s")
        return True
    else:
        print(f"❌ Rate limiter failed: should_limit={should_limit}, wait_time={wait_time}")
        return False

def test_state_manager_deduplication():
    """Test StateManager log deduplication fix."""
    print("\n🧪 Testing StateManager log deduplication...")
    
    from src.state_manager import StateManager
    from pathlib import Path
    import tempfile
    import os
    
    # Use a temporary file for testing
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_path = Path(f.name)
    
    try:
        # Create state manager
        sm = StateManager(temp_path)
        
        # Create new session
        session = sm.create_new_session("test-session")
        
        # Add some log lines
        sm.add_log_line("Test log 1")
        sm.add_log_line("Test log 2")
        
        # Check that session doesn't have duplicate log buffer during runtime
        if session.log_buffer == []:  # Should be empty during runtime
            print("✅ SessionState.log_buffer is empty during runtime (no duplication)")
        else:
            print(f"❌ SessionState.log_buffer has {len(session.log_buffer)} items during runtime")
            return False
        
        # Save and reload to test serialization
        sm.save_state()
        sm2 = StateManager(temp_path)
        loaded_session = sm2.load_state()
        
        if loaded_session and len(sm2.log_buffer) == 2:
            print("✅ Log buffer correctly saved and loaded")
            return True
        else:
            print(f"❌ Log buffer issue: loaded_session={loaded_session is not None}, buffer_len={len(sm2.log_buffer) if loaded_session else 'N/A'}")
            return False
    
    finally:
        # cleanup
        if temp_path.exists():
            os.unlink(temp_path)

def main():
    """Run all tests."""
    print("🚀 Testing RemoCode Audit Fixes\n")
    
    tests = [
        ("Module Imports", test_imports),
        ("Rate Limiter Fix", test_rate_limiter),
        ("StateManager Deduplication", test_state_manager_deduplication),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"💥 {test_name} failed")
        except Exception as e:
            print(f"💥 {test_name} crashed: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes verified successfully!")
        return True
    else:
        print("⚠️  Some fixes need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)