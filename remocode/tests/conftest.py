"""Test configuration and fixtures for RemoCode tests."""

import pytest
import sys
from pathlib import Path

# Add repo root to Python path so imports work correctly
repo_root = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(repo_root))


@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for the test session."""
    import asyncio
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()