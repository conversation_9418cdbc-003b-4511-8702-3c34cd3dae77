[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[notification_handler] Type: , Message: ''
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[notification_handler] Args received: ['/Users/<USER>/Desktop/github/next13-clarify/remocode/v2/hooks/notification_handler.py', '', '']
[notification_handler] Env vars: CLAUDE_NOTIFICATION_TYPE='NOT_SET', CLAUDE_NOTIFICATION_MESSAGE='NOT_SET'
[notification_handler] Processed: Type: '', Message: ''
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
